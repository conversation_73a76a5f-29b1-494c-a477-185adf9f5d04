import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { LoadBalancerStack } from './load-balancer-stack';
import { EcsStack } from './ecs-stack';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';

/**
 * Claude Relay 栈属性接口
 */
export interface ClaudeRelayStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  loadBalancerStack?: LoadBalancerStack;
  ecsStack: EcsStack; // 用于获取共享EFS
  serviceConnectNamespace: servicediscovery.HttpNamespace;
  ecsSecurityGroup: ec2.SecurityGroup;
}

/**
 * Claude Relay 服务栈
 * 独立的 claude-relay 中转服务，专注于业务逻辑
 * 现在自管理负载均衡器路由配置
 */
export class ClaudeRelayStack extends cdk.Stack {
  public readonly service: ecs.Ec2Service;
  public readonly appSecurityGroup: ec2.SecurityGroup;
  public claudeRelayTargetGroup?: elbv2.ApplicationTargetGroup;
  private dataAccessPoint: efs.AccessPoint;
  private logsAccessPoint: efs.AccessPoint;
  private readonly config: EnvironmentConfig;

  constructor(scope: Construct, id: string, props: ClaudeRelayStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, loadBalancerStack, ecsStack, serviceConnectNamespace, ecsSecurityGroup } = props;
    this.config = config;

    // 只有在启用 claude-relay 时才创建资源
    if (!config.claudeRelay.enabled) {
      return;
    }

    // 使用ECS共享EFS文件系统创建Claude-Relay专用访问点
    this.createEfsAccessPoints(config, ecsStack.sharedEfsFileSystem);

    // 创建密钥（如果不存在）
    const jwtSecret = new secretsmanager.Secret(this, 'JwtSecret', {
      secretName: config.claudeRelay.secrets.jwtSecretName,
      description: 'JWT Secret for Claude Relay service',
      generateSecretString: {
        secretStringTemplate: '{}',
        generateStringKey: 'jwt_secret',
        excludeCharacters: '"@/\\\'',
        passwordLength: 32,
      },
    });

    const encryptionKeySecret = new secretsmanager.Secret(this, 'EncryptionKeySecret', {
      secretName: config.claudeRelay.secrets.encryptionKeyName,
      description: 'Encryption Key for Claude Relay service',
      generateSecretString: {
        secretStringTemplate: '{}',
        generateStringKey: 'encryption_key',
        excludeCharacters: '"@/\\\'',
        passwordLength: 32,
      },
    });

    // 创建主应用日志组
    const logGroup = new logs.LogGroup(this, 'LogGroup', {
      logGroupName: config.claudeRelay.logging.logGroupName,
      retention: logs.RetentionDays.ONE_DAY, // 开发环境1天保留
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // 创建任务定义 - 使用BRIDGE网络模式以提高部署密度和简化网络配置
    const taskDefinition = new ecs.Ec2TaskDefinition(this, 'TaskDefinition', {
      networkMode: ecs.NetworkMode.BRIDGE,
    });

    // 为任务定义添加EFS访问权限
    ecsStack.sharedEfsFileSystem.grantRootAccess(taskDefinition.taskRole);

    // 添加EFS卷挂载使用Access Point

    // 添加data目录挂载
    taskDefinition.addVolume({
      name: 'claude-relay-data',
      efsVolumeConfiguration: {
        fileSystemId: ecsStack.sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.dataAccessPoint.accessPointId,
        },
      },
    });

    // 添加logs目录挂载  
    taskDefinition.addVolume({
      name: 'claude-relay-logs',
      efsVolumeConfiguration: {
        fileSystemId: ecsStack.sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.logsAccessPoint.accessPointId,
        },
      },
    });

    // 添加 Claude Relay 容器
    const container = taskDefinition.addContainer('ClaudeRelayContainer', {
      image: ecs.ContainerImage.fromRegistry(
        `${config.claudeRelay.container.image}:${config.claudeRelay.container.tag}`
      ),
      memoryReservationMiB: config.claudeRelay.container.memory,
      cpu: config.claudeRelay.container.cpu,
      // 使用默认启动命令，Redis通过Service Connect访问
      // command: [], // 使用镜像默认命令
      environment: {
        ...config.claudeRelay.environment,
        PORT: config.claudeRelay.service.port.toString(),
        // Redis连接配置 - 使用Service Connect DNS名称
        REDIS_HOST: 'claude-relay-redis', // Service Connect DNS名称
        REDIS_PORT: '6379',
        // Redis连接池和重连配置 - 优化的设置
        REDIS_MAX_RETRIES_PER_REQUEST: '3',
        REDIS_RETRY_DELAY_ON_FAIL_OVER: '1000',
        REDIS_ENABLE_READY_CHECK: 'true', // 启用就绪检查
        REDIS_MAX_RETRIES: '10', // 合理的最大重试次数
        REDIS_RETRY_DELAY: '2000', // 重试延迟
        REDIS_CONNECT_TIMEOUT: '10000', // 连接超时
        REDIS_COMMAND_TIMEOUT: '5000', // 命令超时
        REDIS_LAZYCONNECT: 'false', // 立即连接以快速发现问题
        // 额外的Redis配置
        REDIS_KEEP_ALIVE: '30000',
        REDIS_FAMILY: '4', // 强制使用IPv4
        // 调试信息
        DEBUG: 'true',
        NODE_ENV: config.claudeRelay.environment.NODE_ENV || 'production',
      },
      secrets: {
        JWT_SECRET: ecs.Secret.fromSecretsManager(jwtSecret, 'jwt_secret'),
        ENCRYPTION_KEY: ecs.Secret.fromSecretsManager(encryptionKeySecret, 'encryption_key'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: config.claudeRelay.service.name,
        logGroup: logGroup,
      }),
      healthCheck: {
        command: [
          'CMD-SHELL',
          // 简化健康检查，只检查应用服务，Redis连接通过Service Connect访问
          `curl -f http://localhost:${config.claudeRelay.service.port}${config.claudeRelay.service.healthCheckPath} || exit 1`
        ],
        interval: cdk.Duration.seconds(config.claudeRelay.healthCheck.interval),
        timeout: cdk.Duration.seconds(config.claudeRelay.healthCheck.timeout),
        retries: config.claudeRelay.healthCheck.unhealthyThresholdCount,
        startPeriod: cdk.Duration.seconds(120), // 等待应用启动并连接到Redis服务的时间
      },
    });

    // 挂载EFS卷到容器
    container.addMountPoints({
      sourceVolume: 'claude-relay-data',
      containerPath: '/app/data',
      readOnly: false,
    });

    container.addMountPoints({
      sourceVolume: 'claude-relay-logs', 
      containerPath: '/app/logs',
      readOnly: false,
    });

    // 添加端口映射 - BRIDGE模式下使用动态端口映射
    container.addPortMappings({
      name: config.claudeRelay.service.name,
      containerPort: config.claudeRelay.service.port,
      hostPort: 0, // 使用动态端口分配
      protocol: ecs.Protocol.TCP,
    });


    // BRIDGE网络模式下使用ECS实例的安全组，无需单独的任务级安全组
    // ECS集群安全组已配置ALB访问动态端口范围（32768-65535）
    // Redis通信通过Service Connect DNS名称
    this.appSecurityGroup = ecsSecurityGroup;

    // 创建 ECS 服务（使用BRIDGE网络模式）
    this.service = new ecs.Ec2Service(this, 'Service', {
      cluster,
      taskDefinition,
      desiredCount: config.claudeRelay.ecs.desiredCount,
      serviceName: `${config.claudeRelay.service.name}-${config.environment}`,
      // BRIDGE模式不需要指定VPC子网和安全组，使用ECS实例的网络配置
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: config.claudeRelay.service.name,
          dnsName: 'claude-relay',
          port: config.claudeRelay.service.port,
        }],
      },
    });

    // 目标组附加现在由LoadBalancerStack处理

    // 配置自动扩容（如果启用）
    if (config.claudeRelay.ecs.enableAutoScaling) {
      this.setupAutoScaling();
    }

    // 配置负载均衡器路由（如果LoadBalancerStack可用）
    if (loadBalancerStack) {
      this.configureLoadBalancer(config, vpc, loadBalancerStack);
    }

    // 输出服务信息
    new cdk.CfnOutput(this, 'ServiceArn', {
      value: this.service.serviceArn,
      description: 'Claude Relay ECS Service ARN',
      exportName: `${config.environment}-claude-relay-service-arn`,
    });

    // 输出Service Connect信息
    new cdk.CfnOutput(this, 'ServiceConnectNamespace', {
      value: serviceConnectNamespace.namespaceName,
      description: 'Service Connect namespace for Claude Relay',
      exportName: `${config.environment}-claude-relay-service-connect-namespace`,
    });

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'ClaudeRelay');
    cdk.Tags.of(this).add('Service', 'claude-relay');
  }

  /**
   * 配置负载均衡器路由
   */
  private configureLoadBalancer(
    config: EnvironmentConfig,
    vpc: ec2.Vpc,
    loadBalancerStack: LoadBalancerStack
  ): void {
    // 创建Claude Relay目标组 - 使用INSTANCE目标类型以匹配BRIDGE网络模式
    this.claudeRelayTargetGroup = new elbv2.ApplicationTargetGroup(this, 'ClaudeRelayTargetGroup', {
      vpc,
      port: config.claudeRelay.service.port,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE, // BRIDGE网络模式使用INSTANCE目标类型
      // targetGroupName will be auto-generated by CDK to ensure uniqueness
      healthCheck: {
        enabled: true,
        healthyHttpCodes: config.claudeRelay.healthCheck.healthyHttpCodes,
        path: config.claudeRelay.service.healthCheckPath,
        interval: cdk.Duration.seconds(config.claudeRelay.healthCheck.interval),
        timeout: cdk.Duration.seconds(config.claudeRelay.healthCheck.timeout),
        healthyThresholdCount: config.claudeRelay.healthCheck.healthyThresholdCount,
        unhealthyThresholdCount: config.claudeRelay.healthCheck.unhealthyThresholdCount,
        protocol: elbv2.Protocol.HTTP,
      },
    });

    // 创建监听器规则 - 使用域名路由（仅当配置了域名时）
    if (config.claudeRelay.routing.domain) {
      const hostHeaders = [config.claudeRelay.routing.domain];

      // HTTP 监听器路由
      // 注意：只有在未启用 HTTPS 重定向时才添加，因为启用重定向时，
      // LoadBalancerStack 会将所有 HTTP 请求全局重定向到 HTTPS
      if (loadBalancerStack.publicHttpListener && !config.loadBalancer.ssl.enableHttpsRedirect) {
        new elbv2.ApplicationListenerRule(this, 'ClaudeRelayHttpRule', {
          listener: loadBalancerStack.publicHttpListener,
          priority: config.claudeRelay.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.claudeRelayTargetGroup]),
        });
      }

      // HTTPS 监听器路由（生产环境的主要路由）
      // 当启用 HTTPS 重定向时，所有流量最终都会到达这里
      if (loadBalancerStack.publicHttpsListener) {
        new elbv2.ApplicationListenerRule(this, 'ClaudeRelayHttpsRule', {
          listener: loadBalancerStack.publicHttpsListener,
          priority: config.claudeRelay.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.claudeRelayTargetGroup]),
        });
      }

    }

    // 将服务附加到目标组 - 明确指定容器名称和端口
    this.claudeRelayTargetGroup.addTarget(this.service.loadBalancerTarget({
      containerName: 'ClaudeRelayContainer',
      containerPort: config.claudeRelay.service.port,
    }));

    // 输出Claude Relay访问URL
    if (loadBalancerStack.publicLoadBalancer && config.claudeRelay.routing.domain) {
      const protocol = config.loadBalancer.ssl.enableHttpsRedirect ? 'https' : 'http';
      const url = `${protocol}://${config.claudeRelay.routing.domain}`;
      
      new cdk.CfnOutput(this, 'ClaudeRelayDNS', {
        value: url,
        description: 'Claude Relay Service URL',
        exportName: `${config.environment}-claude-relay-url`,
      });
    }
  }

  /**
   * 设置自动扩容
   */
  private setupAutoScaling(): void {
    const scaling = this.service.autoScaleTaskCount({
      minCapacity: this.config.claudeRelay.ecs.minCapacity,
      maxCapacity: this.config.claudeRelay.ecs.maxCapacity,
    });

    // CPU使用率扩容策略
    scaling.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });

    // 内存使用率扩容策略
    scaling.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 80,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });
  }

  /**
   * 创建EFS访问点
   */
  private createEfsAccessPoints(config: EnvironmentConfig, efsFileSystem: efs.FileSystem): void {
    // 使用root权限避免权限问题，由容器内部管理权限
    // 创建应用数据访问点 /ecs/claude-relay/data
    this.dataAccessPoint = efsFileSystem.addAccessPoint('ClaudeRelayDataAccessPoint', {
      path: '/ecs/claude-relay/data',
      posixUser: {
        uid: '0', // root用户ID
        gid: '0', // root组ID
      },
      createAcl: {
        ownerUid: '0',
        ownerGid: '0',
        permissions: '755',
      },
    });

    // 创建日志访问点 /ecs/claude-relay/logs
    this.logsAccessPoint = efsFileSystem.addAccessPoint('ClaudeRelayLogsAccessPoint', {
      path: '/ecs/claude-relay/logs',
      posixUser: {
        uid: '0', // root用户ID
        gid: '0', // root组ID
      },
      createAcl: {
        ownerUid: '0',
        ownerGid: '0',
        permissions: '755',
      },
    });
  }
  // 负载均衡器路由配置方法已移除，现在由LoadBalancerStack全权负责
}