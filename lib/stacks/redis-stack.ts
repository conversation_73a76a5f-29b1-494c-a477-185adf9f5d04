/**
 * Redis栈 - EC2部署Redis服务
 *
 * 功能特性:
 * - 使用r6g.medium EC2实例进行专用Redis部署
 * - 支持多Redis实例(主从、缓存)配置
 * - ECS Service Connect集成
 */

import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { RedisInstanceConfig } from '../config/stacks';

export interface RedisStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  serviceConnectNamespace?: servicediscovery.HttpNamespace;
  ecsSecurityGroup?: ec2.SecurityGroup;
}

export class RedisStack extends cdk.Stack {
  public readonly redisInstance: ec2.Instance;
  public readonly redisSecurityGroup: ec2.SecurityGroup;
  public readonly redisEndpoints: { [key: string]: string };
  public readonly serviceDiscoveryService?: servicediscovery.Service;
  public readonly redisConfigDocument: ssm.CfnDocument;
  public readonly redisConfigAssociation: ssm.CfnAssociation;

  constructor(scope: Construct, id: string, props: RedisStackProps) {
    super(scope, id, props);

    const { config, vpc, ecsSecurityGroup } = props;
    const redisConfig = config.redis;

    if (!redisConfig.enabled) {
      return;
    }

    // 创建Redis安全组
    this.redisSecurityGroup = this.createRedisSecurityGroup(vpc, redisConfig, ecsSecurityGroup);

    // 创建IAM角色
    const instanceRole = this.createInstanceRole(redisConfig);

    // 创建Parameter Store参数存储Redis配置
    this.createParameterStoreConfig(redisConfig);

    // 创建CloudWatch日志组
    const logGroup = this.createLogGroup(redisConfig);

    // 创建SSM Document进行Redis配置
    this.redisConfigDocument = this.createRedisConfigDocument(redisConfig);

    // 创建EC2实例（不使用UserData）
    this.redisInstance = this.createRedisInstance(
      vpc,
      redisConfig,
      instanceRole
    );

    // 创建SSM Association应用配置
    this.redisConfigAssociation = this.createRedisConfigAssociation(redisConfig);

    // 配置实例端点映射
    this.redisEndpoints = this.createEndpointMapping(redisConfig);

    // 创建服务发现服务
    if (redisConfig.serviceDiscovery.enabled && props.serviceConnectNamespace) {
      this.serviceDiscoveryService = this.createServiceDiscoveryService(
        props.serviceConnectNamespace,
        redisConfig
      );
    }

    // 输出重要信息
    this.createOutputs(redisConfig);
  }

  /**
   * 创建Redis安全组
   */
  private createRedisSecurityGroup(
    vpc: ec2.Vpc,
    redisConfig: EnvironmentConfig['redis'],
    ecsSecurityGroup?: ec2.SecurityGroup
  ): ec2.SecurityGroup {
    const securityGroup = new ec2.SecurityGroup(this, 'RedisSecurityGroup', {
      vpc,
      description: `Redis Security Group - ${this.stackName}`,
      allowAllOutbound: true,
    });

    // 允许从指定CIDR块访问Redis端口
    redisConfig.security.allowedCidrBlocks.forEach((cidr) => {
      redisConfig.instances.forEach((instance) => {
        securityGroup.addIngressRule(
          ec2.Peer.ipv4(cidr),
          ec2.Port.tcp(instance.port),
          `Allow Redis access to port ${instance.port} from ${cidr}`
        );
      });

      // 哨兵端口
      if (redisConfig.sentinel.enabled) {
        securityGroup.addIngressRule(
          ec2.Peer.ipv4(cidr),
          ec2.Port.tcp(redisConfig.sentinel.port),
          `Allow Redis Sentinel access from ${cidr}`
        );
      }
    });

    // 允许ECS安全组访问Redis端口
    if (ecsSecurityGroup) {
      redisConfig.instances.forEach((instance) => {
        securityGroup.addIngressRule(
          ec2.Peer.securityGroupId(ecsSecurityGroup.securityGroupId),
          ec2.Port.tcp(instance.port),
          `Allow ECS access to Redis ${instance.purpose} on port ${instance.port}`
        );
      });

      // 哨兵端口（如果启用）
      if (redisConfig.sentinel.enabled) {
        securityGroup.addIngressRule(
          ec2.Peer.securityGroupId(ecsSecurityGroup.securityGroupId),
          ec2.Port.tcp(redisConfig.sentinel.port),
          'Allow ECS access to Redis Sentinel'
        );
      }
    }

    // SSH访问（如果有密钥对）
    if (redisConfig.node.keyPairName) {
      securityGroup.addIngressRule(
        ec2.Peer.ipv4('10.0.0.0/8'),
        ec2.Port.tcp(22),
        'Allow SSH access from VPC'
      );
    }

    return securityGroup;
  }

  /**
   * 创建EC2实例IAM角色
   */
  private createInstanceRole(
    redisConfig: EnvironmentConfig['redis']
  ): iam.Role {
    const role = new iam.Role(this, 'RedisInstanceRole', {
      assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
      description: `Redis Instance Role - ${this.stackName}`,
    });

    // 基础SSM权限
    role.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore')
    );

    // SSM Parameter Store权限（读取Redis配置参数）
    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ssm:GetParameter',
        'ssm:GetParameters',
        'ssm:GetParametersByPath',
      ],
      resources: [
        `arn:aws:ssm:${this.region}:${this.account}:parameter/redis/${this.stackName}`,
        `arn:aws:ssm:${this.region}:${this.account}:parameter/redis/${this.stackName}/*`,
      ],
    }));

    // SSM Document执行权限
    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ssm:SendCommand',
        'ssm:ListCommandInvocations',
        'ssm:DescribeInstanceInformation',
      ],
      resources: ['*'],
    }));

    // CloudWatch Logs权限
    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
        'logs:DescribeLogStreams',
      ],
      resources: [
        `arn:aws:logs:${this.region}:${this.account}:log-group:${redisConfig.logging.logGroupName}*`,
      ],
    }));

    return role;
  }



  /**
   * 创建Parameter Store参数存储Redis配置
   */
  private createParameterStoreConfig(
    redisConfig: EnvironmentConfig['redis']
  ): void {
    // 存储Redis实例配置
    redisConfig.instances.forEach((instance) => {
      new ssm.StringParameter(this, `RedisInstanceConfig${instance.name}`, {
        parameterName: `/redis/${this.stackName}/${instance.name}/config`,
        stringValue: JSON.stringify({
          port: instance.port,
          purpose: instance.purpose,
          description: instance.description,
          isPrimary: instance.isPrimary,
          maxMemory: instance.maxMemory,
          persistence: instance.persistence,
          replication: instance.replication,
        }),
        tier: ssm.ParameterTier.STANDARD,
        description: `Redis ${instance.name} instance configuration`,
      });
    });

    // 存储全局Redis配置
    new ssm.StringParameter(this, 'RedisGlobalConfig', {
      parameterName: `/redis/${this.stackName}/global/config`,
      stringValue: JSON.stringify({
        sentinel: redisConfig.sentinel,
        backup: redisConfig.backup,
        security: {
          allowedCidrBlocks: redisConfig.security.allowedCidrBlocks,
          tls: redisConfig.security.tls,
        },
        serviceDiscovery: redisConfig.serviceDiscovery,
        logging: redisConfig.logging,
        monitoring: redisConfig.monitoring,
      }),
      tier: ssm.ParameterTier.STANDARD,
      description: `Redis global configuration for ${this.stackName}`,
    });

    // 存储Redis密码（如果启用认证）
    if (redisConfig.security.authPassword) {
      new ssm.StringParameter(this, 'RedisAuthPassword', {
        parameterName: `/redis/${this.stackName}/security/auth-password`,
        stringValue: redisConfig.security.authPassword,
        tier: ssm.ParameterTier.STANDARD,
        description: `Redis authentication password for ${this.stackName}`,
      });
    }
  }

  /**
   * 创建CloudWatch日志组
   */
  private createLogGroup(
    redisConfig: EnvironmentConfig['redis']
  ): logs.LogGroup {
    return new logs.LogGroup(this, 'RedisLogGroup', {
      logGroupName: redisConfig.logging.logGroupName,
      retention: logs.RetentionDays.ONE_MONTH,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });
  }

  /**
   * 创建EC2实例（不使用UserData，改用SSM管理）
   */
  private createRedisInstance(
    vpc: ec2.Vpc,
    redisConfig: EnvironmentConfig['redis'],
    instanceRole: iam.Role
  ): ec2.Instance {
    // 创建实例
    const instance = new ec2.Instance(this, 'RedisInstance', {
      instanceType: ec2.InstanceType.of(
        ec2.InstanceClass.R6G,
        ec2.InstanceSize.MEDIUM
      ),
      machineImage: ec2.MachineImage.latestAmazonLinux2({
        cpuType: ec2.AmazonLinuxCpuType.ARM_64,
      }),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroup: this.redisSecurityGroup,
      role: instanceRole,
      keyPair: redisConfig.node.keyPairName ? ec2.KeyPair.fromKeyPairName(this, 'RedisKeyPair', redisConfig.node.keyPairName) : undefined,
      blockDevices: [
        {
          deviceName: '/dev/xvda',
          volume: ec2.BlockDeviceVolume.ebs(
            redisConfig.node.rootVolumeSize,
            {
              volumeType: ec2.EbsDeviceVolumeType.GP3,
              encrypted: redisConfig.node.encryptedVolumes,
            }
          ),
        },
        {
          deviceName: '/dev/xvdf',
          volume: ec2.BlockDeviceVolume.ebs(
            redisConfig.node.dataVolumeSize,
            {
              volumeType: ec2.EbsDeviceVolumeType.GP3,
              iops: redisConfig.node.iops,
              encrypted: redisConfig.node.encryptedVolumes,
            }
          ),
        },
      ],
    });

    // 添加标签
    cdk.Tags.of(instance).add('Name', `Redis-${this.stackName}`);
    cdk.Tags.of(instance).add('Service', 'Redis');
    cdk.Tags.of(instance).add('Environment', this.stackName.includes('-dev') ? 'dev' : 'prod');
    cdk.Tags.of(instance).add('ConfigManagement', 'SSM');

    return instance;
  }

  /**
   * 创建SSM Document进行Redis配置
   */
  private createRedisConfigDocument(
    redisConfig: EnvironmentConfig['redis']
  ): ssm.CfnDocument {
    const documentContent = {
      schemaVersion: '2.2',
      description: 'Configure Redis instances with idempotent operations',
      parameters: {
        StackName: {
          type: 'String',
          description: 'Stack name for parameter store path',
          default: this.stackName,
        },
      },
      mainSteps: [
        {
          action: 'aws:runShellScript',
          name: 'installRedis',
          description: 'Install Redis server with idempotent operations',
          inputs: {
            timeoutSeconds: '600',
            runCommand: [
              '#!/bin/bash',
              'set -e',
              'echo "Starting Redis installation and configuration..."',
              '',
              '# Function to log with timestamp',
              'log() {',
              '  echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"',
              '}',
              '',
              '# Check if Redis is already installed',
              'if ! command -v redis-server &> /dev/null; then',
              '  log "Installing Redis dependencies..."',
              '  yum update -y',
              '  yum install -y gcc gcc-c++ make tcl wget jq',
              '  ',
              '  log "Downloading and compiling Redis..."',
              '  cd /tmp',
              '  wget -q http://download.redis.io/redis-stable.tar.gz',
              '  tar xzf redis-stable.tar.gz',
              '  cd redis-stable',
              '  make',
              '  make install',
              '  log "Redis installation completed"',
              'else',
              '  log "Redis already installed, skipping installation"',
              'fi',
              '',
              '# Create Redis user if not exists',
              'if ! id redis &>/dev/null; then',
              '  log "Creating Redis user..."',
              '  useradd -r -s /bin/false redis',
              'fi',
              '',
              '# Create directory structure',
              'log "Setting up directory structure..."',
              'mkdir -p /etc/redis /var/lib/redis /var/log/redis',
              'chown redis:redis /var/lib/redis /var/log/redis',
            ],
          },
        },
        {
          action: 'aws:runShellScript',
          name: 'setupDataVolume',
          description: 'Setup data volume with idempotent operations',
          inputs: {
            timeoutSeconds: '300',
            runCommand: [
              '#!/bin/bash',
              'set -e',
              '',
              'log() {',
              '  echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"',
              '}',
              '',
              '# Check if data volume is already mounted',
              'if ! mountpoint -q /redis-data; then',
              '  log "Setting up data volume..."',
              '  ',
              '  # Check if device is already formatted',
              '  if ! blkid /dev/xvdf; then',
              '    log "Formatting data volume..."',
              '    mkfs.ext4 /dev/xvdf',
              '  else',
              '    log "Data volume already formatted"',
              '  fi',
              '  ',
              '  # Mount the volume',
              '  mkdir -p /redis-data',
              '  mount /dev/xvdf /redis-data',
              '  ',
              '  # Add to fstab if not already present',
              '  if ! grep -q "/dev/xvdf" /etc/fstab; then',
              '    echo "/dev/xvdf /redis-data ext4 defaults,nofail 0 2" >> /etc/fstab',
              '  fi',
              '  ',
              '  log "Data volume mounted successfully"',
              'else',
              '  log "Data volume already mounted"',
              'fi',
              '',
              '# Set proper ownership',
              'chown -R redis:redis /redis-data',
            ],
          },
        },
        {
          action: 'aws:runShellScript',
          name: 'configureRedisInstances',
          description: 'Configure Redis instances from Parameter Store',
          inputs: {
            timeoutSeconds: '600',
            runCommand: [
              '#!/bin/bash',
              'set -e',
              '',
              'log() {',
              '  echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"',
              '}',
              '',
              `STACK_NAME="${this.stackName}"`,
              'REGION=$(curl -s http://***************/latest/meta-data/placement/region)',
              '',
              '# Function to get parameter from Parameter Store',
              'get_parameter() {',
              '  aws ssm get-parameter --name "$1" --region "$REGION" --query "Parameter.Value" --output text',
              '}',
              '',
              '# Get Redis instances configuration',
              'log "Retrieving Redis configuration from Parameter Store..."',
              'INSTANCE_NAMES=$(aws ssm get-parameters-by-path --path "/redis/$STACK_NAME" --region "$REGION" --query "Parameters[?contains(Name, \'/config\')].Name" --output text | grep -E "/redis/$STACK_NAME/[^/]+/config$" | sed "s|/redis/$STACK_NAME/||g" | sed "s|/config||g")',
              '',
              'for INSTANCE_NAME in $INSTANCE_NAMES; do',
              '  log "Configuring Redis instance: $INSTANCE_NAME"',
              '  ',
              '  # Get instance configuration',
              '  INSTANCE_CONFIG=$(get_parameter "/redis/$STACK_NAME/$INSTANCE_NAME/config")',
              '  ',
              '  # Parse configuration using jq',
              '  PORT=$(echo "$INSTANCE_CONFIG" | jq -r ".port")',
              '  MAX_MEMORY=$(echo "$INSTANCE_CONFIG" | jq -r ".maxMemory")',
              '  ENABLE_RDB=$(echo "$INSTANCE_CONFIG" | jq -r ".persistence.enableRdb")',
              '  RDB_INTERVAL=$(echo "$INSTANCE_CONFIG" | jq -r ".persistence.rdbSaveInterval")',
              '  ENABLE_AOF=$(echo "$INSTANCE_CONFIG" | jq -r ".persistence.enableAof")',
              '  AOF_FSYNC=$(echo "$INSTANCE_CONFIG" | jq -r ".persistence.aofFsync")',
              '  IS_PRIMARY=$(echo "$INSTANCE_CONFIG" | jq -r ".isPrimary")',
              '  ',
              '  # Create instance data directory',
              '  mkdir -p "/redis-data/$INSTANCE_NAME"',
              '  chown redis:redis "/redis-data/$INSTANCE_NAME"',
              '  ',
              '  # Generate Redis configuration file',
              '  CONFIG_FILE="/etc/redis/$INSTANCE_NAME.conf"',
              '  cat > "$CONFIG_FILE" << EOF',
              '# Redis $INSTANCE_NAME configuration - Generated by SSM',
              'port $PORT',
              'bind 0.0.0.0',
              'dir /redis-data/$INSTANCE_NAME',
              'logfile /var/log/redis/redis-$INSTANCE_NAME.log',
              'pidfile /var/run/redis-$INSTANCE_NAME.pid',
              'maxmemory ${MAX_MEMORY}mb',
              'maxmemory-policy allkeys-lru',
              'daemonize no',
              'EOF',
              '  ',
              '  # Add persistence configuration',
              '  if [ "$ENABLE_RDB" = "true" ]; then',
              '    echo "save $RDB_INTERVAL 1" >> "$CONFIG_FILE"',
              '  else',
              '    echo \'save ""\' >> "$CONFIG_FILE"',
              '  fi',
              '  ',
              '  if [ "$ENABLE_AOF" = "true" ]; then',
              '    echo "appendonly yes" >> "$CONFIG_FILE"',
              '    echo "appendfsync $AOF_FSYNC" >> "$CONFIG_FILE"',
              '  else',
              '    echo "appendonly no" >> "$CONFIG_FILE"',
              '  fi',
              '  ',
              '  # Add replication configuration if not primary',
              '  if [ "$IS_PRIMARY" != "true" ]; then',
              '    REPLICATION_CONFIG=$(echo "$INSTANCE_CONFIG" | jq -r ".replication")',
              '    if [ "$REPLICATION_CONFIG" != "null" ]; then',
              '      MASTER_HOST=$(echo "$REPLICATION_CONFIG" | jq -r ".masterHost")',
              '      MASTER_PORT=$(echo "$REPLICATION_CONFIG" | jq -r ".masterPort")',
              '      if [ "$MASTER_HOST" != "null" ] && [ "$MASTER_PORT" != "null" ]; then',
              '        echo "replicaof $MASTER_HOST $MASTER_PORT" >> "$CONFIG_FILE"',
              '      fi',
              '    fi',
              '  fi',
              '  ',
              '  # Add authentication if configured',
              '  AUTH_PASSWORD=$(aws ssm get-parameter --name "/redis/$STACK_NAME/security/auth-password" --region "$REGION" --query "Parameter.Value" --output text 2>/dev/null || echo "")',
              '  if [ -n "$AUTH_PASSWORD" ] && [ "$AUTH_PASSWORD" != "None" ]; then',
              '    echo "requirepass $AUTH_PASSWORD" >> "$CONFIG_FILE"',
              '  fi',
              '  ',
              '  # Set proper ownership',
              '  chown redis:redis "$CONFIG_FILE"',
              '  ',
              '  log "Configuration file created for $INSTANCE_NAME"',
              'done',
            ],
          },
        },
        {
          action: 'aws:runShellScript',
          name: 'createSystemdServices',
          description: 'Create systemd services for Redis instances',
          inputs: {
            timeoutSeconds: '300',
            runCommand: [
              '#!/bin/bash',
              'set -e',
              '',
              'log() {',
              '  echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"',
              '}',
              '',
              `STACK_NAME="${this.stackName}"`,
              'REGION=$(curl -s http://***************/latest/meta-data/placement/region)',
              '',
              '# Get instance names',
              'INSTANCE_NAMES=$(aws ssm get-parameters-by-path --path "/redis/$STACK_NAME" --region "$REGION" --query "Parameters[?contains(Name, \'/config\')].Name" --output text | grep -E "/redis/$STACK_NAME/[^/]+/config$" | sed "s|/redis/$STACK_NAME/||g" | sed "s|/config||g")',
              '',
              'for INSTANCE_NAME in $INSTANCE_NAMES; do',
              '  log "Creating systemd service for $INSTANCE_NAME"',
              '  ',
              '  # Get port from configuration',
              '  INSTANCE_CONFIG=$(aws ssm get-parameter --name "/redis/$STACK_NAME/$INSTANCE_NAME/config" --region "$REGION" --query "Parameter.Value" --output text)',
              '  PORT=$(echo "$INSTANCE_CONFIG" | jq -r ".port")',
              '  ',
              '  # Stop service if running to ensure clean update',
              '  SERVICE_FILE="/etc/systemd/system/redis-$INSTANCE_NAME.service"',
              '  SERVICE_NAME="redis-$INSTANCE_NAME"',
              '  if systemctl is-active --quiet "$SERVICE_NAME"; then',
              '    log "Stopping $SERVICE_NAME for configuration update"',
              '    systemctl stop "$SERVICE_NAME"',
              '  fi',
              '  ',
              '  # Create systemd service file',
              '  cat > "$SERVICE_FILE" << EOF',
              '[Unit]',
              'Description=Redis $INSTANCE_NAME Server',
              'After=network.target',
              '',
              '[Service]',
              'Type=simple',
              'ExecStart=/usr/local/bin/redis-server /etc/redis/$INSTANCE_NAME.conf',
              'ExecStop=/usr/local/bin/redis-cli -p $PORT shutdown',
              'TimeoutStartSec=30',
              'TimeoutStopSec=30',
              'Restart=always',
              'User=redis',
              'Group=redis',
              'RuntimeDirectory=redis',
              'RuntimeDirectoryMode=0755',
              '',
              '[Install]',
              'WantedBy=multi-user.target',
              'EOF',
              '  ',
              '  log "Service file created for $INSTANCE_NAME"',
              'done',
              '',
              '# Reload systemd daemon to pick up new service files',
              'log "Reloading systemd daemon"',
              'systemctl daemon-reload',
            ],
          },
        },
        {
          action: 'aws:runShellScript',
          name: 'startRedisServices',
          description: 'Start Redis services with idempotent operations',
          inputs: {
            timeoutSeconds: '300',
            runCommand: [
              '#!/bin/bash',
              'set -e',
              '',
              'log() {',
              '  echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"',
              '}',
              '',
              `STACK_NAME="${this.stackName}"`,
              'REGION=$(curl -s http://***************/latest/meta-data/placement/region)',
              '',
              '# Reload systemd daemon',
              'systemctl daemon-reload',
              '',
              '# Get instance names',
              'INSTANCE_NAMES=$(aws ssm get-parameters-by-path --path "/redis/$STACK_NAME" --region "$REGION" --query "Parameters[?contains(Name, \'/config\')].Name" --output text | grep -E "/redis/$STACK_NAME/[^/]+/config$" | sed "s|/redis/$STACK_NAME/||g" | sed "s|/config||g")',
              '',
              'for INSTANCE_NAME in $INSTANCE_NAMES; do',
              '  SERVICE_NAME="redis-$INSTANCE_NAME"',
              '  log "Managing service: $SERVICE_NAME"',
              '  ',
              '  # Enable service',
              '  systemctl enable "$SERVICE_NAME"',
              '  ',
              '  # Check if service is already running',
              '  if systemctl is-active --quiet "$SERVICE_NAME"; then',
              '    log "$SERVICE_NAME is already running, restarting to apply new configuration"',
              '    systemctl restart "$SERVICE_NAME"',
              '  else',
              '    log "Starting $SERVICE_NAME"',
              '    systemctl start "$SERVICE_NAME"',
              '  fi',
              '  ',
              '  # Wait for service to start',
              '  sleep 2',
              '  ',
              '  # Verify service status',
              '  if systemctl is-active --quiet "$SERVICE_NAME"; then',
              '    log "$SERVICE_NAME started successfully"',
              '  else',
              '    log "ERROR: Failed to start $SERVICE_NAME"',
              '    systemctl status "$SERVICE_NAME" || true',
              '    exit 1',
              '  fi',
              'done',
              '',
              'log "All Redis services configured and started successfully"',
            ],
          },
        },
      ],
    };

    return new ssm.CfnDocument(this, 'RedisConfigDocument', {
      documentType: 'Command',
      documentFormat: 'JSON',
      content: documentContent,
      tags: [
        {
          key: 'Environment',
          value: this.stackName.includes('-dev') ? 'dev' : 'prod',
        },
        {
          key: 'Service',
          value: 'Redis',
        },
        {
          key: 'ConfigManagement',
          value: 'SSM',
        },
      ],
    });
  }

  /**
   * 创建SSM Association实现配置自动应用和监控
   */
  private createRedisConfigAssociation(
    redisConfig: EnvironmentConfig['redis']
  ): ssm.CfnAssociation {
    return new ssm.CfnAssociation(this, 'RedisConfigAssociation', {
      name: this.redisConfigDocument.ref,
      targets: [
        {
          key: 'InstanceIds',
          values: [this.redisInstance.instanceId],
        },
      ],
      parameters: {
        StackName: [this.stackName],
      },
      scheduleExpression: 'rate(30 minutes)', // 每30分钟检查一次配置
      associationName: `Redis-Config-Association-${this.stackName}`,
      complianceSeverity: 'MEDIUM',
      maxConcurrency: '1',
      maxErrors: '0',
      // 移除S3输出位置，使用默认的CloudWatch Logs
    });
  }

  /**
   * 创建端点映射 - 按业务用途组织
   */
  private createEndpointMapping(
    redisConfig: EnvironmentConfig['redis']
  ): { [key: string]: string } {
    const endpoints: { [key: string]: string } = {};

    redisConfig.instances.forEach((instance) => {
      // 基础端点
      endpoints[instance.name] = `${this.redisInstance.instancePrivateIp}:${instance.port}`;

      // 按业务用途分组的端点
      const purposeKey = `${instance.purpose}`;
      if (!endpoints[purposeKey]) {
        endpoints[purposeKey] = `${this.redisInstance.instancePrivateIp}:${instance.port}`;
      }

      // 如果是从实例，为主实例用途添加读取端点
      if (!instance.isPrimary) {
        const readKey = `${instance.purpose}-read`;
        if (!endpoints[readKey]) {
          endpoints[readKey] = `${this.redisInstance.instancePrivateIp}:${instance.port}`;
        } else {
          // 多个从实例时，用逗号分隔
          endpoints[readKey] += `,${this.redisInstance.instancePrivateIp}:${instance.port}`;
        }
      }
    });

    if (redisConfig.sentinel.enabled) {
      endpoints['sentinel'] = `${this.redisInstance.instancePrivateIp}:${redisConfig.sentinel.port}`;
    }

    return endpoints;
  }

  /**
   * 创建服务发现服务 - 支持业务特定域名
   */
  private createServiceDiscoveryService(
    namespace: servicediscovery.HttpNamespace,
    redisConfig: EnvironmentConfig['redis']
  ): servicediscovery.Service {
    const mainService = new servicediscovery.Service(this, 'RedisServiceDiscovery', {
      namespace,
      name: 'redis',
      description: `Redis Service Discovery for ${this.stackName}`,
    });

    // 为每个业务用途创建独立的服务发现服务
    const purposeServices: { [key: string]: servicediscovery.Service } = {};
    const uniquePurposes = [...new Set(redisConfig.instances.map(inst => inst.purpose))];

    uniquePurposes.forEach((purpose) => {
      const serviceName = `redis-${purpose}`;
      purposeServices[purpose] = new servicediscovery.Service(this, `RedisServiceDiscovery${purpose}`, {
        namespace,
        name: serviceName,
        description: `Redis ${purpose} Service Discovery for ${this.stackName}`,
      });

      // 输出业务特定的服务发现信息
      new cdk.CfnOutput(this, `RedisServiceDiscovery${purpose}Name`, {
        value: serviceName,
        description: `Redis ${purpose} service discovery name`,
      });
    });

    return mainService;
  }



  /**
   * 创建输出
   */
  private createOutputs(redisConfig: EnvironmentConfig['redis']): void {
    new cdk.CfnOutput(this, 'RedisInstanceId', {
      value: this.redisInstance.instanceId,
      description: 'Redis EC2 Instance ID',
    });

    new cdk.CfnOutput(this, 'RedisPrivateIp', {
      value: this.redisInstance.instancePrivateIp,
      description: 'Redis Instance Private IP',
    });

    // SSM相关输出
    new cdk.CfnOutput(this, 'RedisConfigDocumentName', {
      value: this.redisConfigDocument.ref,
      description: 'SSM Document name for Redis configuration',
    });

    new cdk.CfnOutput(this, 'RedisConfigAssociationId', {
      value: this.redisConfigAssociation.ref,
      description: 'SSM Association ID for Redis configuration',
    });

    new cdk.CfnOutput(this, 'RedisParameterStoreBasePath', {
      value: `/redis/${this.stackName}`,
      description: 'Parameter Store base path for Redis configuration',
    });

    Object.entries(this.redisEndpoints).forEach(([name, endpoint]) => {
      // 将端点名称格式化为适合CloudFormation输出的格式
      const outputName = name.charAt(0).toUpperCase() + name.slice(1).replace(/[^a-zA-Z0-9]/g, '');
      new cdk.CfnOutput(this, `RedisEndpoint${outputName}`, {
        value: endpoint,
        description: `Redis ${name} endpoint`,
      });
    });

    // 输出业务特定的连接信息
    const uniquePurposes = [...new Set(redisConfig.instances.map(inst => inst.purpose))];
    uniquePurposes.forEach((purpose) => {
      const purposeName = purpose.charAt(0).toUpperCase() + purpose.slice(1).replace(/[^a-zA-Z0-9]/g, '');

      // 输出主实例端点
      const masterInstance = redisConfig.instances.find(inst => inst.purpose === purpose && inst.isPrimary);
      if (masterInstance) {
        new cdk.CfnOutput(this, `Redis${purposeName}MasterEndpoint`, {
          value: `${this.redisInstance.instancePrivateIp}:${masterInstance.port}`,
          description: `Redis ${purpose} master endpoint`,
        });
      }

      // 输出从实例端点（如果存在）
      const slaveInstances = redisConfig.instances.filter(inst => inst.purpose === purpose && !inst.isPrimary);
      if (slaveInstances.length > 0) {
        const slaveEndpoints = slaveInstances.map(inst => `${this.redisInstance.instancePrivateIp}:${inst.port}`).join(',');
        new cdk.CfnOutput(this, `Redis${purposeName}SlaveEndpoints`, {
          value: slaveEndpoints,
          description: `Redis ${purpose} slave endpoints`,
        });
      }

      // 输出服务发现域名（如果启用）
      if (redisConfig.serviceDiscovery.enabled && redisConfig.serviceDiscovery.domainMappings?.[purpose]) {
        new cdk.CfnOutput(this, `Redis${purposeName}ServiceDomain`, {
          value: redisConfig.serviceDiscovery.domainMappings[purpose],
          description: `Redis ${purpose} service discovery domain`,
        });
      }
    });

  }

}