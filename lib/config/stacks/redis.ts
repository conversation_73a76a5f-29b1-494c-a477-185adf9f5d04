/**
 * Redis栈配置接口
 */

import { LoggingConfig, MonitoringConfigBase } from '../shared';

export interface RedisInstanceConfig {
  /** 实例名称 */
  name: string;
  /** Redis端口 */
  port: number;
  /** 业务用途 */
  purpose: 'claude-relay' | 'session' | 'cache' | 'general';
  /** 用途描述 */
  description: string;
  /** 是否为主实例 */
  isPrimary?: boolean;
  /** 内存限制(MB) */
  maxMemory: number;
  /** 持久化配置 */
  persistence: {
    /** 启用RDB快照 */
    enableRdb: boolean;
    /** RDB保存频率(秒) */
    rdbSaveInterval: number;
    /** 启用AOF日志 */
    enableAof: boolean;
    /** AOF同步策略 */
    aofFsync: 'always' | 'everysec' | 'no';
  };
  /** 复制配置 */
  replication?: {
    /** 主实例地址 */
    masterHost?: string;
    /** 主实例端口 */
    masterPort?: number;
    /** 复制密码 */
    masterAuth?: string;
  };
}

export interface RedisSentinelConfig {
  /** 启用哨兵模式 */
  enabled: boolean;
  /** 哨兵端口 */
  port: number;
  /** 监控的主服务名称 */
  masterName: string;
  /** 故障转移仲裁数 */
  quorum: number;
}

export interface RedisBackupConfig {
  /** 启用S3备份 */
  enabled: boolean;
  /** S3存储桶名称 */
  bucketName?: string;
  /** 备份保留天数 */
  retentionDays: number;
  /** 备份频率(cron表达式) */
  scheduleExpression: string;
  /** 启用备份加密 */
  encryptionEnabled: boolean;
  /** 自动还原配置 */
  autoRestore?: {
    /** 启用自动还原 */
    enabled: boolean;
    /** 还原点标识符 */
    restorePointTag?: string;
  };
}

export interface RedisSecurityConfig {
  /** Redis密码认证 */
  authPassword?: string;
  /** 允许的客户端IP范围 */
  allowedCidrBlocks: string[];
  /** SSL/TLS配置 */
  tls?: {
    enabled: boolean;
    certificateArn?: string;
  };
}

export interface RedisServiceDiscoveryConfig {
  /** 启用服务发现 */
  enabled: boolean;
  /** 主实例域名 */
  masterDomain: string;
  /** 从实例域名模板 */
  slaveDomainTemplate: string;
  /** 哨兵域名 */
  sentinelDomain?: string;
  /** TTL设置 */
  ttl: number;
  /** 业务特定域名映射 */
  domainMappings?: { [purpose: string]: string };
}

export interface RedisConfig {
  /** 启用Redis服务 */
  enabled: boolean;

  /** EC2节点配置 */
  node: {
    /** 实例类型 */
    instanceType: string;
    /** 密钥对名称 */
    keyPairName?: string;
    /** 根卷大小(GB) */
    rootVolumeSize: number;
    /** 数据卷大小(GB) */
    dataVolumeSize: number;
    /** EBS卷类型 */
    volumeType: 'gp3' | 'io1' | 'io2';
    /** IOPS配置 */
    iops?: number;
    /** 启用卷加密 */
    encryptedVolumes: boolean;
  };

  /** Redis实例配置列表 */
  instances: RedisInstanceConfig[];

  /** 哨兵配置 */
  sentinel: RedisSentinelConfig;

  /** 备份配置 */
  backup: RedisBackupConfig;

  /** 安全配置 */
  security: RedisSecurityConfig;

  /** 服务发现配置 */
  serviceDiscovery: RedisServiceDiscoveryConfig;

  /** 日志配置 */
  logging: LoggingConfig;

  /** 监控配置 */
  monitoring: MonitoringConfigBase & {
    /** CloudWatch指标收集间隔 */
    metricsCollectionInterval: number;
    /** 自定义指标启用 */
    customMetricsEnabled: boolean;
  };
}