/**
 * 开发环境 - Redis栈配置
 */

import { RedisConfig } from '../../stacks';

export const redisConfig: RedisConfig = {
  enabled: true,

  // EC2节点配置 - 开发环境使用较小配置
  node: {
    instanceType: 'r6g.medium',
    keyPairName: 'yuanhui-dev-keypair',
    rootVolumeSize: 20,
    dataVolumeSize: 50,
    volumeType: 'gp3',
    iops: 3000,
    encryptedVolumes: false, // 开发环境不加密以降低成本
  },

  // Redis实例配置 - 按业务用途分离
  instances: [
    {
      name: 'claude-relay',
      port: 6379,
      purpose: 'claude-relay',
      description: '<PERSON> Relay服务专用Redis实例，用于存储应用数据',
      isPrimary: true,
      maxMemory: 1024, // 1GB - Claude <PERSON>需要较多内存
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 900, // 15分钟 - 数据重要，需要持久化
        enableAof: true,
        aofFsync: 'everysec',
      },
    },
    {
      name: 'session',
      port: 6380,
      purpose: 'session',
      description: 'Yherp和Khmall应用session存储专用Redis实例',
      isPrimary: true,
      maxMemory: 1536, // 1.5GB - Session数据量可能较大
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 1800, // 30分钟 - Session数据相对重要
        enableAof: false, // Session数据丢失影响相对较小
        aofFsync: 'no',
      },
    },
    {
      name: 'cache',
      port: 6381,
      purpose: 'cache',
      description: '通用缓存Redis实例，用于临时数据缓存',
      isPrimary: true,
      maxMemory: 512, // 512MB - 纯缓存，内存适中
      persistence: {
        enableRdb: false, // 纯缓存不需要持久化
        rdbSaveInterval: 0,
        enableAof: false,
        aofFsync: 'no',
      },
    },
  ],

  // 哨兵配置 - 开发环境简化配置
  sentinel: {
    enabled: false, // 开发环境暂时不启用
    port: 26379,
    masterName: 'redis-master-dev',
    quorum: 1,
  },

  // 备份配置
  backup: {
    enabled: true,
    bucketName: 'yuanhui-redis-backup-dev',
    retentionDays: 7, // 开发环境保留7天
    scheduleExpression: 'cron(0 2 * * ? *)', // 每天凌晨2点
    encryptionEnabled: false,
    autoRestore: {
      enabled: false,
    },
  },

  // 安全配置
  security: {
    allowedCidrBlocks: [
      '10.0.0.0/8', // VPC内部访问
    ],
    tls: {
      enabled: false, // 开发环境不启用TLS
    },
  },

  // 服务发现配置 - 按业务用途命名
  serviceDiscovery: {
    enabled: true,
    masterDomain: 'redis.yuanhui-dev.local', // 通用域名
    slaveDomainTemplate: 'redis-{purpose}.yuanhui-dev.local', // 按用途命名
    sentinelDomain: 'redis-sentinel.yuanhui-dev.local',
    ttl: 60,
    // 业务特定域名
    domainMappings: {
      'claude-relay': 'redis-claude-relay.yuanhui-dev.local',
      'session': 'redis-session.yuanhui-dev.local',
      'cache': 'redis-cache.yuanhui-dev.local',
    },
  },

  // 日志配置
  logging: {
    retentionDays: 30,
    logGroupName: '/aws/ec2/redis/dev',
  },

  // 监控配置
  monitoring: {
    enableDetailedMonitoring: false, // 开发环境不启用详细监控
    metricsCollectionInterval: 300, // 5分钟
    customMetricsEnabled: false,
  },
};