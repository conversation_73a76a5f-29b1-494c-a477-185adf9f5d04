/**
 * 开发环境 - <PERSON>栈配置
 */

import { ClaudeRelayConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'dev';
const resourceLimits = getResourceByEnv(env, 'medium');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const claudeRelayConfig: ClaudeRelayConfig = {
  enabled: true, // 启用以测试内置Redis功能
  service: {
    name: 'claude-relay',
    port: defaultPorts.claudeRelay,
    healthCheckPath: '/health',
  },
  container: {
    image: 'weishaw/claude-relay-service',
    tag: 'latest',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  ecs: {
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 2,
    enableAutoScaling: false, // 开发环境关闭自动扩容
  },
  routing: {
    domain: generateDomain('claude-relay', env, 'kh2u.com'),
    pathPatterns: [], // 不再使用路径模式，改用域名路由
    priority: 100,
  },
  environment: {
    NODE_ENV: 'production',
    LOG_LEVEL: 'info',
    ADMIN_USER: 'admin',
    ADMIN_PASSWORD: 'admin123',
  },
  secrets: {
    jwtSecretName: `claude-relay-jwt-secret-${env}`,
    encryptionKeyName: `claude-relay-encryption-key-${env}`,
  },
  healthCheck: {
    healthyHttpCodes: '200,404',
    interval: 30,
    timeout: 10,
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 5,
  },
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/claude-relay-${env}`,
  },
  monitoring: {
    enableDetailedMonitoring: false,
    enableXRayTracing: false,
  },
};