/**
 * 开发环境配置主入口
 */

import { EnvironmentConfig } from '../../shared';
import { networkConfig } from './network';
import { databaseConfig } from './database';
import { ecsConfig } from './ecs';
import { redisConfig } from './redis';
import { rabbitmqConfig } from './rabbitmq';
import { airflowConfig } from './airflow';
import { loadBalancerConfig } from './load-balancer';
import { applicationConfig } from './application';
import { claudeRelayConfig } from './claude-relay';
import { wikijsConfig } from './wikijs';
import { swarmConfig } from './swarm';
import { cloudfrontConfig } from './cloudfront';
import { monitoringConfig } from './monitoring';
import { backupConfig } from './backup';
import { turnkey } from './turnkey';

export const devConfig: EnvironmentConfig = {
  region: 'ap-east-2',
  account: process.env.CDK_DEFAULT_ACCOUNT || '',
  environment: 'dev',

  network: networkConfig,
  database: databaseConfig,
  ecs: ecsConfig,
  redis: redisConfig,
  rabbitmq: rabbitmqConfig,
  airflow: airflowConfig,
  cloudfront: cloudfrontConfig,
  monitoring: monitoringConfig,
  swarm: swarmConfig,
  loadBalancer: loadBalancerConfig,
  claudeRelay: claudeRelayConfig,
  wikijs: wikijsConfig,
  backup: backupConfig,
  turnkey: turnkey,
};

// 导出接口类型以保持向后兼容
export interface DevEnvironmentConfig extends EnvironmentConfig {}