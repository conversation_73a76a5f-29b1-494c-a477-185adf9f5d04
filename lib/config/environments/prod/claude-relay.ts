/**
 * 生产环境 - <PERSON>栈配置
 */

import { ClaudeRelayConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'prod';
const resourceLimits = getResourceByEnv(env, 'small');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const claudeRelayConfig: ClaudeRelayConfig = {
  enabled: true,
  service: {
    name: 'claude-relay',
    port: defaultPorts.claudeRelay,
    healthCheckPath: '/health',
  },
  container: {
    image: 'weishaw/claude-relay-service',
    tag: 'v1.1.132',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  ecs: {
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 1,
    enableAutoScaling: true, // 生产环境启用自动扩容
  },
  routing: {
    domain: generateDoma<PERSON>('claude-relay', env, 'kh2u.com'),
    pathPatterns: [], // 不再使用路径模式，改用域名路由
    priority: 100,
  },
  environment: {
    NODE_ENV: 'production',
    LOG_LEVEL: 'warn',
  },
  secrets: {
    jwtSecretName: `claude-relay-jwt-secret-${env}`,
    encryptionKeyName: `claude-relay-encryption-key-${env}`,
  },
  healthCheck: {
    healthyHttpCodes: '200,404',
    interval: 30,
    timeout: 5,
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 3,
  },
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/claude-relay-${env}`,
  },
  monitoring: {
    enableDetailedMonitoring: true,
    enableXRayTracing: false,
  },
};