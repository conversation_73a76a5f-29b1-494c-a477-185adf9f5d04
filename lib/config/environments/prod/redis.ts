/**
 * 生产环境 - Redis栈配置
 */

import { RedisConfig } from '../../stacks';
import { 
  getVpcConfigByEnv, 
} from '../../shared';

const env = 'prod';
const vpcConfig = getVpcConfigByEnv(env);

export const redisConfig: RedisConfig = {
  enabled: true,

  // EC2节点配置 - 生产环境使用更高配置
  node: {
    instanceType: 'r6g.large',
    keyPairName: 'ec2',
    rootVolumeSize: 20,
    dataVolumeSize: 10,
    volumeType: 'gp3',
    // Redis内存优先，磁盘仅用于持久化，使用基础IOPS即可
    encryptedVolumes: false,
  },

  // Redis实例配置 - 按业务用途分离，高可用配置
  instances: [
    {
      name: 'claude-relay',
      port: 6379,
      purpose: 'claude-relay',
      description: '<PERSON>服务专用Redis实例，用于存储应用数据',
      isPrimary: true,
      maxMemory: 2048, // 2GB - <PERSON>生产环境
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 300, // 5分钟 - 数据重要，需要频繁持久化
        enableAof: true,
        aofFsync: 'everysec',
      },
    },
    {
      name: 'claude-relay-slave',
      port: 6380,
      purpose: 'claude-relay',
      description: 'Claude Relay Redis从实例，提供读取负载分担',
      isPrimary: false,
      maxMemory: 2048, // 2GB
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 600, // 10分钟
        enableAof: true, // 生产环境从实例也启用AOF
        aofFsync: 'everysec',
      },
      replication: {
        masterHost: 'localhost',
        masterPort: 6379,
      },
    },
    {
      name: 'session',
      port: 6381,
      purpose: 'session',
      description: 'Yherp和Khmall应用session存储专用Redis实例',
      isPrimary: true,
      maxMemory: 3072, // 3GB - Session数据量可能较大，生产环境需更多内存
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 600, // 10分钟 - Session数据相对重要
        enableAof: true, // 生产环境启用AOF提高可靠性
        aofFsync: 'everysec',
      },
    },
    {
      name: 'session-slave',
      port: 6382,
      purpose: 'session',
      description: 'Session Redis从实例，提供读取负载分担',
      isPrimary: false,
      maxMemory: 3072, // 3GB
      persistence: {
        enableRdb: true,
        rdbSaveInterval: 900, // 15分钟
        enableAof: false, // Session从实例可以不启用AOF降低写入压力
        aofFsync: 'no',
      },
      replication: {
        masterHost: 'localhost',
        masterPort: 6381,
      },
    },
    {
      name: 'cache',
      port: 6383,
      purpose: 'cache',
      description: '通用缓存Redis实例，用于临时数据缓存',
      isPrimary: true,
      maxMemory: 2048, // 2GB - 纯缓存，内存适中
      persistence: {
        enableRdb: false, // 纯缓存不需要持久化
        rdbSaveInterval: 0,
        enableAof: false,
        aofFsync: 'no',
      },
    },
  ],

  // 哨兵配置 - 生产环境启用高可用
  sentinel: {
    enabled: true,
    port: 26379,
    masterName: 'redis-master-prod',
    quorum: 2,
  },

  // 备份配置
  backup: {
    enabled: true,
    bucketName: 'yuanhui-redis-backup-prod',
    retentionDays: 30, // 生产环境保留30天
    scheduleExpression: 'cron(0 1 * * ? *)', // 每天凌晨1点
    encryptionEnabled: true, // 生产环境启用备份加密
    autoRestore: {
      enabled: true,
      restorePointTag: 'latest-stable',
    },
  },

  // 安全配置
  security: {
    authPassword: '${redis-auth-password}', // 生产环境启用密码认证
    allowedCidrBlocks: [
      vpcConfig.cidr, // VPC内部访问
    ],
    tls: {
      enabled: true, // 生产环境启用TLS
    },
  },

  // 服务发现配置 - 按业务用途命名
  serviceDiscovery: {
    enabled: true,
    masterDomain: 'redis.yuanhui-prod.local', // 通用域名
    slaveDomainTemplate: 'redis-{purpose}.yuanhui-prod.local', // 按用途命名
    sentinelDomain: 'redis-sentinel.yuanhui-prod.local',
    ttl: 30, // 生产环境更短TTL
    // 业务特定域名映射
    domainMappings: {
      'claude-relay': 'redis-claude-relay.yuanhui-prod.local',
      'session': 'redis-session.yuanhui-prod.local',
      'cache': 'redis-cache.yuanhui-prod.local',
    },
  },

  // 日志配置
  logging: {
    retentionDays: 90, // 生产环境保留3个月
    logGroupName: '/aws/ec2/redis/prod',
  },

  // 监控配置
  monitoring: {
    enableDetailedMonitoring: true, // 生产环境启用详细监控
    enableXRayTracing: true,
    metricsCollectionInterval: 60, // 1分钟
    customMetricsEnabled: true,
  },
};