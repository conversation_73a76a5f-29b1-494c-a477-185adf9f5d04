# Redis 缓存服务

## 概述

Redis是一个开源的内存数据结构存储系统，用作数据库、缓存和消息代理。本项目在单个EC2实例上部署多个Redis实例，按业务用途分离，为Yuan Hui应用栈提供缓存、会话存储和数据持久化服务。

## 服务架构

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Redis EC2 Instance                               │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │
│  │ Claude <PERSON>    │  │    Session      │  │     Cache       │             │
│  │   Redis         │  │     Redis       │  │     Redis       │             │
│  │  Port: 6379     │  │  Port: 6380     │  │  Port: 6381     │             │
│  │  Memory: 1-2GB  │  │  Memory: 1.5-3GB│  │  Memory: 512MB-2GB│            │
│  │  Persistence: ✓ │  │  Persistence: ✓ │  │  Persistence: ✗ │             │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘             │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────┐│
│  │                        Data Directory Structure                         ││
│  │    /redis-data/claude-relay/    /redis-data/session/    /redis-data/cache/ ││
│  │           (EBS Volume Mount: /redis-data)                               ││
│  └─────────────────────────────────────────────────────────────────────────┘│
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────┐│
│  │                        EC2 Instance Details                            ││
│  │                                                                         ││
│  │  • Instance Type: r6g.medium (dev) / r6g.large (prod)                  ││
│  │  • Architecture: ARM64 (Graviton2)                                     ││
│  │  • Storage: 50-100GB GP3 EBS Volume                                    ││
│  │  • Network: Private subnet with VPC security groups                   ││
│  │  • Service Discovery: ECS Service Connect integration                  ││
│  └─────────────────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────────────────┘
```

## 部署配置

### EC2实例配置
- **实例类型**:
  - 开发环境: r6g.medium (1 vCPU, 8 GiB Memory)
  - 生产环境: r6g.large (2 vCPU, 16 GiB Memory)
- **AMI**: Amazon Linux 2 (ARM64)
- **存储**:
  - 根卷: 20-30 GB GP3
  - 数据卷: 50-100 GB GP3 (挂载到 /redis-data)
- **网络**: 私有子网部署，通过NAT Gateway访问互联网

### Redis实例配置

#### 开发环境
| 实例名称 | 端口 | 业务用途 | 内存限制 | 持久化 | 描述 |
|---------|------|----------|----------|--------|------|
| claude-relay | 6379 | Claude Relay | 1GB | RDB+AOF | Claude Relay服务专用数据存储 |
| session | 6380 | 会话存储 | 1.5GB | RDB | Yherp和Khmall应用会话存储 |
| cache | 6381 | 通用缓存 | 512MB | 无 | 临时数据缓存 |

#### 生产环境
| 实例名称 | 端口 | 业务用途 | 内存限制 | 持久化 | 高可用 |
|---------|------|----------|----------|--------|--------|
| claude-relay | 6379 | Claude Relay (主) | 2GB | RDB+AOF | 是 |
| claude-relay-slave | 6380 | Claude Relay (从) | 2GB | RDB+AOF | 读取负载 |
| session | 6381 | 会话存储 (主) | 3GB | RDB+AOF | 是 |
| session-slave | 6382 | 会话存储 (从) | 3GB | RDB | 读取负载 |
| cache | 6383 | 通用缓存 | 2GB | 无 | 否 |

### 持久化配置
```bash
# Claude Relay Redis 配置 (关键数据)
save 300 1       # 5分钟内有1次写入则保存 (生产环境)
save 900 1       # 15分钟内有1次写入则保存 (开发环境)
appendonly yes   # 启用AOF持久化
appendfsync everysec  # 每秒同步AOF文件
maxmemory-policy allkeys-lru

# Session Redis 配置 (会话数据)
save 600 1       # 10分钟保存间隔
appendonly yes   # 生产环境启用AOF
maxmemory-policy allkeys-lru

# Cache Redis 配置 (纯缓存)
save ""          # 不启用RDB
appendonly no    # 不启用AOF
maxmemory-policy allkeys-lru  # LRU淘汰策略
```

## 服务发现

### 内部DNS和端口映射

Redis实例通过ECS Service Connect进行服务发现，每个业务用途有独立的域名：

#### 开发环境
| 业务用途 | 服务发现域名 | 端口 | 直接IP访问 |
|---------|-------------|------|-----------|
| Claude Relay | redis-claude-relay.yuanhui-dev.local | 6379 | \<redis-ip\>:6379 |
| 会话存储 | redis-session.yuanhui-dev.local | 6380 | \<redis-ip\>:6380 |
| 通用缓存 | redis-cache.yuanhui-dev.local | 6381 | \<redis-ip\>:6381 |

#### 生产环境
| 业务用途 | 主实例域名 | 从实例域名 | 端口 |
|---------|----------|----------|------|
| Claude Relay | redis-claude-relay.yuanhui-prod.local | (主从复制) | 6379/6380 |
| 会话存储 | redis-session.yuanhui-prod.local | (主从复制) | 6381/6382 |
| 通用缓存 | redis-cache.yuanhui-prod.local | - | 6383 |

### 按业务用途连接示例

#### Claude Relay Redis连接
```python
# Python连接示例 - Claude Relay专用
import redis

# 开发环境
claude_redis_dev = redis.Redis(
    host='redis-claude-relay.yuanhui-dev.local',
    port=6379,
    decode_responses=True,
    db=0  # 使用数据库0
)

# 生产环境 (支持主从)
claude_redis_prod = redis.Redis(
    host='redis-claude-relay.yuanhui-prod.local',
    port=6379,
    decode_responses=True,
    db=0
)

# 测试连接
claude_redis_dev.ping()
```

#### 会话存储Redis连接
```python
# Python连接示例 - Session专用
import redis

# 开发环境
session_redis_dev = redis.Redis(
    host='redis-session.yuanhui-dev.local',
    port=6380,
    decode_responses=True,
    db=0
)

# 生产环境
session_redis_prod = redis.Redis(
    host='redis-session.yuanhui-prod.local',
    port=6381,
    decode_responses=True,
    db=0
)

# Session操作示例
def set_session(session_id, data, expire=3600):
    session_redis_dev.setex(f"session:{session_id}", expire, json.dumps(data))

def get_session(session_id):
    data = session_redis_dev.get(f"session:{session_id}")
    return json.loads(data) if data else None
```

#### 通用缓存Redis连接
```python
# Python连接示例 - Cache专用
import redis
import json

# 开发环境
cache_redis_dev = redis.Redis(
    host='redis-cache.yuanhui-dev.local',
    port=6381,
    decode_responses=True,
    db=0
)

# 缓存操作示例
def cache_set(key, value, expire=3600):
    """设置缓存"""
    cache_redis_dev.setex(key, expire, json.dumps(value))

def cache_get(key):
    """获取缓存"""
    cached_value = cache_redis_dev.get(key)
    return json.loads(cached_value) if cached_value else None

def cache_delete(key):
    """删除缓存"""
    return cache_redis_dev.delete(key)
```

### 命令行连接示例
```bash
# 开发环境
redis-cli -h redis-claude-relay.yuanhui-dev.local -p 6379   # Claude Relay
redis-cli -h redis-session.yuanhui-dev.local -p 6380        # Session
redis-cli -h redis-cache.yuanhui-dev.local -p 6381          # Cache

# 生产环境
redis-cli -h redis-claude-relay.yuanhui-prod.local -p 6379  # Claude Relay主
redis-cli -h redis-session.yuanhui-prod.local -p 6381       # Session主
redis-cli -h redis-cache.yuanhui-prod.local -p 6383         # Cache
```

## 本地访问Redis实例

由于Redis实例部署在私有子网中，本地开发时需要通过SSH隧道或堡垒机访问。

### 方法1: SSH隧道访问 (推荐)

#### 获取Redis实例IP地址
```bash
# 查看Redis实例信息
aws ec2 describe-instances \
  --filters "Name=tag:Name,Values=Redis-yuanhui-*-dev" \
  --query 'Reservations[].Instances[].[InstanceId,PrivateIpAddress,State.Name,Tags[?Key==`Name`].Value|[0]]' \
  --output table

# 或通过CloudFormation输出查看
aws cloudformation describe-stacks \
  --stack-name YuanhuiRedis-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`RedisPrivateIp`].OutputValue' \
  --output text
```

#### 设置SSH隧道
```bash
# 方法1: 通过堡垒机建立隧道 (如果有堡垒机)
ssh -L 6379:<redis-private-ip>:6379 \
    -L 6380:<redis-private-ip>:6380 \
    -L 6381:<redis-private-ip>:6381 \
    -i ~/.ssh/your-key.pem \
    ec2-user@<bastion-host-ip>

# 方法2: 通过Session Manager建立隧道 (推荐)
aws ssm start-session \
  --target <redis-instance-id> \
  --document-name AWS-StartPortForwardingSession \
  --parameters '{"portNumber":["6379"],"localPortNumber":["16379"]}'

# 为所有Redis端口建立隧道
aws ssm start-session \
  --target <redis-instance-id> \
  --document-name AWS-StartPortForwardingSession \
  --parameters '{"portNumber":["6379"],"localPortNumber":["16379"]}'

aws ssm start-session \
  --target <redis-instance-id> \
  --document-name AWS-StartPortForwardingSession \
  --parameters '{"portNumber":["6380"],"localPortNumber":["16380"]}'

aws ssm start-session \
  --target <redis-instance-id> \
  --document-name AWS-StartPortForwardingSession \
  --parameters '{"portNumber":["6381"],"localPortNumber":["16381"]}'
```

#### 本地连接Redis
```bash
# 隧道建立后，本地连接
redis-cli -h localhost -p 16379   # Claude Relay
redis-cli -h localhost -p 16380   # Session
redis-cli -h localhost -p 16381   # Cache

# Python连接示例
import redis

# 本地通过SSH隧道连接
local_claude_redis = redis.Redis(
    host='localhost',
    port=16379,
    decode_responses=True,
    db=0
)

local_session_redis = redis.Redis(
    host='localhost',
    port=16380,
    decode_responses=True,
    db=0
)

local_cache_redis = redis.Redis(
    host='localhost',
    port=16381,
    decode_responses=True,
    db=0
)

# 测试连接
local_claude_redis.ping()
```

### 方法2: 直接SSH到Redis实例

#### SSH连接到Redis实例
```bash
# 通过Session Manager连接 (推荐)
aws ssm start-session --target <redis-instance-id>

# 或通过SSH连接 (需要配置SSH密钥和堡垒机)
ssh -i ~/.ssh/your-key.pem ec2-user@<redis-private-ip>
```

#### 在Redis实例上操作
```bash
# 连接到实例后，直接使用redis-cli
redis-cli -p 6379   # Claude Relay
redis-cli -p 6380   # Session
redis-cli -p 6381   # Cache

# 查看Redis配置
redis-cli -p 6379 CONFIG GET "*"

# 查看Redis信息
redis-cli -p 6379 INFO

# 查看所有键
redis-cli -p 6379 KEYS "*"

# 监控Redis命令
redis-cli -p 6379 MONITOR
```

### 方法3: 通过ECS Exec访问应用容器

如果需要从应用角度访问Redis，可以通过ECS Exec进入应用容器：

```bash
# 列出ECS任务
aws ecs list-tasks --cluster yuanhui-odoo-dev --service yherp-dev

# 通过ECS Exec进入容器
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task <task-arn> \
  --container yherp \
  --interactive \
  --command "/bin/bash"

# 在容器内测试Redis连接
redis-cli -h redis-claude-relay.yuanhui-dev.local -p 6379
```

### 本地开发配置示例

#### Python应用配置
```python
# config.py - 本地开发配置
import os

class Config:
    # 根据环境选择Redis连接
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'local')

    if ENVIRONMENT == 'local':
        # 本地开发 - 通过SSH隧道
        REDIS_CLAUDE_HOST = 'localhost'
        REDIS_CLAUDE_PORT = 16379
        REDIS_SESSION_HOST = 'localhost'
        REDIS_SESSION_PORT = 16380
        REDIS_CACHE_HOST = 'localhost'
        REDIS_CACHE_PORT = 16381
    elif ENVIRONMENT == 'dev':
        # 开发环境 - 直接连接
        REDIS_CLAUDE_HOST = 'redis-claude-relay.yuanhui-dev.local'
        REDIS_CLAUDE_PORT = 6379
        REDIS_SESSION_HOST = 'redis-session.yuanhui-dev.local'
        REDIS_SESSION_PORT = 6380
        REDIS_CACHE_HOST = 'redis-cache.yuanhui-dev.local'
        REDIS_CACHE_PORT = 6381
    elif ENVIRONMENT == 'prod':
        # 生产环境
        REDIS_CLAUDE_HOST = 'redis-claude-relay.yuanhui-prod.local'
        REDIS_CLAUDE_PORT = 6379
        REDIS_SESSION_HOST = 'redis-session.yuanhui-prod.local'
        REDIS_SESSION_PORT = 6381
        REDIS_CACHE_HOST = 'redis-cache.yuanhui-prod.local'
        REDIS_CACHE_PORT = 6383

# 使用配置
import redis
from config import Config

claude_redis = redis.Redis(
    host=Config.REDIS_CLAUDE_HOST,
    port=Config.REDIS_CLAUDE_PORT,
    decode_responses=True
)
```

#### Docker Compose本地开发
```yaml
# docker-compose.local.yml - 本地开发环境
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=local
      - REDIS_CLAUDE_HOST=host.docker.internal
      - REDIS_CLAUDE_PORT=16379
      - REDIS_SESSION_HOST=host.docker.internal
      - REDIS_SESSION_PORT=16380
      - REDIS_CACHE_HOST=host.docker.internal
      - REDIS_CACHE_PORT=16381
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

### 故障排除

#### SSH隧道问题
```bash
# 检查隧道是否建立
lsof -i :16379
netstat -an | grep 16379

# 测试隧道连通性
telnet localhost 16379

# 重新建立隧道
pkill -f "aws ssm start-session"
# 然后重新运行隧道命令
```

#### Session Manager问题
```bash
# 确保Session Manager插件已安装
session-manager-plugin

# 更新Session Manager插件
curl "https://s3.amazonaws.com/session-manager-downloads/plugin/latest/mac/sessionmanager-bundle.zip" -o "sessionmanager-bundle.zip"
unzip sessionmanager-bundle.zip
sudo ./sessionmanager-bundle/install -i /usr/local/sessionmanagerplugin -b /usr/local/bin/session-manager-plugin

# 检查IAM权限
aws ssm describe-sessions --state Active
```

## Odoo集成

Odoo应用栈集成多个Redis实例，按业务用途分离以提高性能和可靠性。

### 会话存储配置

#### 开发环境配置
```ini
# odoo.conf - 开发环境
[options]
# 会话存储配置 - 使用专用Session Redis
session_store = redis
session_redis_host = redis-session.yuanhui-dev.local
session_redis_port = 6380
session_redis_password =
session_redis_prefix = odoo_session:
session_redis_expiration = 7200  # 2小时

# 缓存配置 - 使用专用Cache Redis
cache_redis_host = redis-cache.yuanhui-dev.local
cache_redis_port = 6381
cache_redis_db = 0
```

#### 生产环境配置
```ini
# odoo.conf - 生产环境
[options]
# 会话存储配置 - 使用专用Session Redis主实例
session_store = redis
session_redis_host = redis-session.yuanhui-prod.local
session_redis_port = 6381
session_redis_password = ${session-redis-password}
session_redis_prefix = odoo_session:
session_redis_expiration = 7200  # 2小时

# 缓存配置 - 使用专用Cache Redis
cache_redis_host = redis-cache.yuanhui-prod.local
cache_redis_port = 6383
cache_redis_db = 0
```

### Python集成示例

#### Odoo Redis连接管理器
```python
# addons/redis_integration/models/redis_manager.py
import redis
import json
import logging
from odoo import api, models, tools

_logger = logging.getLogger(__name__)

class RedisConnectionManager(models.AbstractModel):
    _name = 'redis.connection.manager'
    _description = 'Redis Connection Manager'

    @api.model
    def _get_redis_config(self):
        """获取Redis配置"""
        environment = tools.config.get('environment', 'dev')

        if environment == 'dev':
            return {
                'claude_relay': {
                    'host': 'redis-claude-relay.yuanhui-dev.local',
                    'port': 6379,
                    'db': 0
                },
                'session': {
                    'host': 'redis-session.yuanhui-dev.local',
                    'port': 6380,
                    'db': 0
                },
                'cache': {
                    'host': 'redis-cache.yuanhui-dev.local',
                    'port': 6381,
                    'db': 0
                }
            }
        else:  # production
            return {
                'claude_relay': {
                    'host': 'redis-claude-relay.yuanhui-prod.local',
                    'port': 6379,
                    'db': 0,
                    'password': tools.config.get('claude_redis_password')
                },
                'session': {
                    'host': 'redis-session.yuanhui-prod.local',
                    'port': 6381,
                    'db': 0,
                    'password': tools.config.get('session_redis_password')
                },
                'cache': {
                    'host': 'redis-cache.yuanhui-prod.local',
                    'port': 6383,
                    'db': 0
                }
            }

    @api.model
    def get_redis_connection(self, purpose='cache'):
        """获取指定用途的Redis连接"""
        config = self._get_redis_config()
        redis_config = config.get(purpose, config['cache'])

        try:
            return redis.Redis(
                host=redis_config['host'],
                port=redis_config['port'],
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
        except Exception as e:
            _logger.error(f"Failed to connect to Redis ({purpose}): {e}")
            return None

    @api.model
    def get_claude_redis(self):
        """获取Claude Relay专用Redis连接"""
        return self.get_redis_connection('claude_relay')

    @api.model
    def get_session_redis(self):
        """获取Session专用Redis连接"""
        return self.get_redis_connection('session')

    @api.model
    def get_cache_redis(self):
        """获取Cache专用Redis连接"""
        return self.get_redis_connection('cache')
```

#### 缓存服务实现
```python
# addons/redis_integration/models/cache_service.py
import json
import logging
from datetime import datetime, timedelta
from odoo import api, models, tools

_logger = logging.getLogger(__name__)

class RedisCacheService(models.AbstractModel):
    _name = 'redis.cache.service'
    _description = 'Redis Cache Service'

    @api.model
    def _get_cache_key(self, namespace, key):
        """生成缓存键"""
        return f"odoo:cache:{namespace}:{key}"

    @api.model
    def set_cache(self, namespace, key, value, expire=3600, purpose='cache'):
        """设置缓存

        Args:
            namespace: 命名空间 (例如: 'product', 'partner')
            key: 缓存键
            value: 缓存值
            expire: 过期时间(秒)
            purpose: Redis实例用途 ('cache', 'claude_relay', 'session')
        """
        redis_manager = self.env['redis.connection.manager']
        redis_client = redis_manager.get_redis_connection(purpose)

        if not redis_client:
            return False

        try:
            cache_key = self._get_cache_key(namespace, key)
            serialized_value = json.dumps({
                'data': value,
                'timestamp': datetime.now().isoformat(),
                'expire_at': (datetime.now() + timedelta(seconds=expire)).isoformat()
            })

            result = redis_client.setex(cache_key, expire, serialized_value)
            _logger.debug(f"Cache set: {cache_key} (expire: {expire}s)")
            return result

        except Exception as e:
            _logger.error(f"Failed to set cache {namespace}:{key}: {e}")
            return False

    @api.model
    def get_cache(self, namespace, key, purpose='cache'):
        """获取缓存"""
        redis_manager = self.env['redis.connection.manager']
        redis_client = redis_manager.get_redis_connection(purpose)

        if not redis_client:
            return None

        try:
            cache_key = self._get_cache_key(namespace, key)
            cached_value = redis_client.get(cache_key)

            if not cached_value:
                return None

            cached_data = json.loads(cached_value)
            _logger.debug(f"Cache hit: {cache_key}")
            return cached_data['data']

        except Exception as e:
            _logger.error(f"Failed to get cache {namespace}:{key}: {e}")
            return None

    @api.model
    def delete_cache(self, namespace, key=None, purpose='cache'):
        """删除缓存

        Args:
            namespace: 命名空间
            key: 缓存键，如果为None则删除整个命名空间
            purpose: Redis实例用途
        """
        redis_manager = self.env['redis.connection.manager']
        redis_client = redis_manager.get_redis_connection(purpose)

        if not redis_client:
            return False

        try:
            if key:
                # 删除单个键
                cache_key = self._get_cache_key(namespace, key)
                result = redis_client.delete(cache_key)
                _logger.debug(f"Cache deleted: {cache_key}")
                return result
            else:
                # 删除整个命名空间
                pattern = self._get_cache_key(namespace, '*')
                keys = redis_client.keys(pattern)
                if keys:
                    result = redis_client.delete(*keys)
                    _logger.debug(f"Cache namespace deleted: {namespace} ({len(keys)} keys)")
                    return result
                return 0

        except Exception as e:
            _logger.error(f"Failed to delete cache {namespace}:{key}: {e}")
            return False

    @api.model
    def get_cache_stats(self, purpose='cache'):
        """获取缓存统计信息"""
        redis_manager = self.env['redis.connection.manager']
        redis_client = redis_manager.get_redis_connection(purpose)

        if not redis_client:
            return {}

        try:
            info = redis_client.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'hit_rate': round(
                    info.get('keyspace_hits', 0) /
                    max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1) * 100,
                    2
                )
            }
        except Exception as e:
            _logger.error(f"Failed to get cache stats: {e}")
            return {}
```

#### 使用示例
```python
# 在其他模块中使用缓存服务
class ProductTemplate(models.Model):
    _inherit = 'product.template'

    @api.model
    def get_featured_products(self):
        """获取特色产品 - 使用缓存"""
        cache_service = self.env['redis.cache.service']

        # 尝试从缓存获取
        cached_products = cache_service.get_cache('product', 'featured_products')
        if cached_products:
            return cached_products

        # 缓存未命中，查询数据库
        products = self.search([('is_featured', '=', True)]).read(['name', 'list_price', 'image_1920'])

        # 存储到缓存，1小时过期
        cache_service.set_cache('product', 'featured_products', products, expire=3600)

        return products

    def write(self, vals):
        """更新产品时清理相关缓存"""
        result = super().write(vals)

        # 如果更新了特色产品状态，清理缓存
        if 'is_featured' in vals:
            cache_service = self.env['redis.cache.service']
            cache_service.delete_cache('product', 'featured_products')

        return result
```

### 使用场景

- **Claude Relay专用**: Claude Relay服务的应用数据存储，要求高可靠性
- **会话存储**: Yherp和Khmall用户登录会话持久化，支持主从复制
- **通用缓存**: 频繁查询结果缓存、计数器、临时数据存储
- **性能优化**: 通过业务分离减少Redis实例间的互相影响
- **高可用支持**: 生产环境支持主从复制和故障转移

## 管理和监控

### 通过EC2实例管理Redis

#### 连接到Redis实例
```bash
# 通过Session Manager连接到Redis EC2实例
aws ssm start-session --target <redis-instance-id>

# 或通过SSH连接 (需要配置密钥)
ssh -i ~/.ssh/your-key.pem ec2-user@<redis-private-ip>
```

#### Redis实例管理
```bash
# 查看所有Redis进程
ps aux | grep redis

# 查看Redis服务状态
sudo systemctl status redis-claude-relay
sudo systemctl status redis-session
sudo systemctl status redis-cache

# 重启特定Redis实例
sudo systemctl restart redis-claude-relay

# 查看Redis配置文件
cat /etc/redis/claude-relay.conf
cat /etc/redis/session.conf
cat /etc/redis/cache.conf

# 查看Redis日志
sudo tail -f /var/log/redis/redis-claude-relay.log
sudo tail -f /var/log/redis/redis-session.log
sudo tail -f /var/log/redis/redis-cache.log
```

#### Redis CLI操作
```bash
# 连接到不同的Redis实例
redis-cli -p 6379   # Claude Relay
redis-cli -p 6380   # Session (dev) / Claude Relay Slave (prod)
redis-cli -p 6381   # Cache (dev) / Session (prod)
redis-cli -p 6382   # Session Slave (prod only)
redis-cli -p 6383   # Cache (prod only)

# 常用管理命令
redis-cli -p 6379 INFO                    # 查看服务器信息
redis-cli -p 6379 INFO memory             # 查看内存使用
redis-cli -p 6379 INFO persistence        # 查看持久化状态
redis-cli -p 6379 INFO replication        # 查看复制状态 (生产环境)
redis-cli -p 6379 DBSIZE                  # 查看键总数
redis-cli -p 6379 KEYS "*"                # 查看所有键（谨慎使用）
redis-cli -p 6379 MONITOR                 # 监控命令执行

# 按业务用途查看键
redis-cli -p 6379 KEYS "claude:*"         # Claude Relay相关键
redis-cli -p 6380 KEYS "odoo_session:*"   # Session相关键
redis-cli -p 6381 KEYS "cache:*"          # Cache相关键
```

### 内存使用监控

#### 按实例监控内存使用
```bash
# 查看各Redis实例的内存统计
redis-cli -p 6379 INFO memory | grep used_memory_human  # Claude Relay
redis-cli -p 6380 INFO memory | grep used_memory_human  # Session/Slave
redis-cli -p 6381 INFO memory | grep used_memory_human  # Cache/Session

# 查看键空间统计
redis-cli -p 6379 INFO keyspace
redis-cli -p 6380 INFO keyspace
redis-cli -p 6381 INFO keyspace

# 检查大键
redis-cli -p 6379 --bigkeys
redis-cli -p 6380 --bigkeys
redis-cli -p 6381 --bigkeys

# 查看特定键的内存使用
redis-cli -p 6379 MEMORY USAGE "claude:relay:data:key"
redis-cli -p 6380 MEMORY USAGE "odoo_session:user123"
redis-cli -p 6381 MEMORY USAGE "cache:product:featured"
```

#### 生产环境主从监控
```bash
# 查看主从复制状态
redis-cli -p 6379 INFO replication  # Claude Relay主
redis-cli -p 6380 INFO replication  # Claude Relay从
redis-cli -p 6381 INFO replication  # Session主
redis-cli -p 6382 INFO replication  # Session从

# 检查主从同步延迟
redis-cli -p 6379 INFO replication | grep master_repl_offset
redis-cli -p 6380 INFO replication | grep slave_repl_offset
```

### 性能监控

#### 延迟和性能统计
```bash
# 查看各实例的延迟统计 (在EC2实例上运行)
redis-cli -p 6379 --latency-history -i 1  # Claude Relay
redis-cli -p 6380 --latency-history -i 1  # Session/Slave
redis-cli -p 6381 --latency-history -i 1  # Cache/Session

# 查看连接统计
redis-cli -p 6379 INFO clients
redis-cli -p 6380 INFO clients
redis-cli -p 6381 INFO clients

# 查看命令统计
redis-cli -p 6379 INFO commandstats
redis-cli -p 6380 INFO commandstats
redis-cli -p 6381 INFO commandstats

# 查看键空间命中率
redis-cli -p 6379 INFO stats | grep keyspace_
redis-cli -p 6380 INFO stats | grep keyspace_
redis-cli -p 6381 INFO stats | grep keyspace_
```

#### 系统资源监控
```bash
# 查看EC2实例资源使用
top -p $(pgrep redis-server)
htop -p $(pgrep redis-server)

# 查看磁盘使用
df -h /redis-data
du -sh /redis-data/*

# 查看网络连接
netstat -tlnp | grep redis-server
ss -tlnp | grep redis-server
```

## 数据管理

### 备份操作

#### 手动备份
```bash
# 为不同Redis实例触发RDB快照
redis-cli -p 6379 BGSAVE  # Claude Relay
redis-cli -p 6380 BGSAVE  # Session/Slave
redis-cli -p 6381 BGSAVE  # Cache/Session

# 检查备份状态
redis-cli -p 6379 INFO persistence | grep rdb_bgsave
redis-cli -p 6380 INFO persistence | grep rdb_bgsave
redis-cli -p 6381 INFO persistence | grep rdb_bgsave

# 备份RDB文件
sudo cp /redis-data/claude-relay/dump.rdb /backup/claude-relay-$(date +%Y%m%d).rdb
sudo cp /redis-data/session/dump.rdb /backup/session-$(date +%Y%m%d).rdb
sudo cp /redis-data/cache/dump.rdb /backup/cache-$(date +%Y%m%d).rdb

# 备份AOF文件 (如果启用)
sudo cp /redis-data/claude-relay/appendonly.aof /backup/claude-relay-aof-$(date +%Y%m%d).aof
sudo cp /redis-data/session/appendonly.aof /backup/session-aof-$(date +%Y%m%d).aof
```

#### 自动备份脚本
```bash
#!/bin/bash
# /home/<USER>/backup-redis.sh
BACKUP_DIR="/backup/redis"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份Claude Relay (重要数据)
redis-cli -p 6379 BGSAVE
sleep 30
sudo cp /redis-data/claude-relay/dump.rdb $BACKUP_DIR/claude-relay-$DATE.rdb

# 备份Session数据
redis-cli -p 6380 BGSAVE
sleep 30
sudo cp /redis-data/session/dump.rdb $BACKUP_DIR/session-$DATE.rdb

# 清理7天前的备份
find $BACKUP_DIR -name "*.rdb" -mtime +7 -delete

echo "Redis backup completed: $DATE"
```

### 数据清理

#### 按业务用途清理
```bash
# 清空特定Redis实例的特定数据库
redis-cli -p 6379 SELECT 0 && redis-cli -p 6379 FLUSHDB  # Claude Relay
redis-cli -p 6380 SELECT 0 && redis-cli -p 6380 FLUSHDB  # Session
redis-cli -p 6381 SELECT 0 && redis-cli -p 6381 FLUSHDB  # Cache

# 删除匹配的键 (按业务用途)
redis-cli -p 6379 --scan --pattern "claude:temp:*" | xargs redis-cli -p 6379 del
redis-cli -p 6380 --scan --pattern "odoo_session:expired:*" | xargs redis-cli -p 6380 del
redis-cli -p 6381 --scan --pattern "cache:old:*" | xargs redis-cli -p 6381 del

# 批量设置键的过期时间
redis-cli -p 6381 --scan --pattern "cache:*" | while read key; do
    redis-cli -p 6381 EXPIRE "$key" 3600
done
```

#### 清理过期缓存脚本
```python
#!/usr/bin/env python3
# /home/<USER>/cleanup-redis.py
import redis
import sys
from datetime import datetime

# Redis连接配置
REDIS_INSTANCES = {
    'claude-relay': {'host': 'localhost', 'port': 6379, 'db': 0},
    'session': {'host': 'localhost', 'port': 6380, 'db': 0},
    'cache': {'host': 'localhost', 'port': 6381, 'db': 0}
}

def cleanup_redis_instance(name, config):
    """清理指定Redis实例的过期数据"""
    try:
        r = redis.Redis(host=config['host'], port=config['port'], db=config['db'])

        print(f"Cleaning up {name} Redis instance...")

        if name == 'cache':
            # 缓存实例：清理没有过期时间的键
            keys = r.keys('cache:*')
            cleaned = 0
            for key in keys:
                if r.ttl(key) == -1:  # 没有设置过期时间
                    r.expire(key, 3600)  # 设置1小时过期
                    cleaned += 1
            print(f"  Set expiration for {cleaned} cache keys")

        elif name == 'session':
            # 会话实例：清理即将过期的会话
            keys = r.keys('odoo_session:*')
            cleaned = 0
            for key in keys:
                if r.ttl(key) < 300:  # 5分钟内过期
                    r.delete(key)
                    cleaned += 1
            print(f"  Cleaned {cleaned} expiring session keys")

        elif name == 'claude-relay':
            # Claude Relay：只清理临时数据
            keys = r.keys('claude:temp:*')
            cleaned = 0
            for key in keys:
                if r.ttl(key) == -1:
                    r.expire(key, 86400)  # 设置24小时过期
                    cleaned += 1
            print(f"  Set expiration for {cleaned} temporary keys")

        # 获取清理后的统计信息
        info = r.info()
        print(f"  Memory used: {info['used_memory_human']}")
        print(f"  Keys: {info.get('db0', {}).get('keys', 0)}")

    except Exception as e:
        print(f"Error cleaning {name}: {e}")

def main():
    print(f"Starting Redis cleanup at {datetime.now()}")

    for name, config in REDIS_INSTANCES.items():
        cleanup_redis_instance(name, config)

    print("Redis cleanup completed")

if __name__ == "__main__":
    main()
```

## 故障排除

### 常见问题

#### 1. Redis服务无法启动
```bash
# 检查Redis服务状态
sudo systemctl status redis-claude-relay
sudo systemctl status redis-session
sudo systemctl status redis-cache

# 查看详细错误日志
sudo journalctl -u redis-claude-relay -f
sudo tail -f /var/log/redis/redis-claude-relay.log

# 检查配置文件语法
redis-server /etc/redis/claude-relay.conf --test-config

# 检查端口占用
netstat -tlnp | grep :6379
ss -tlnp | grep :6379
```

#### 2. 连接被拒绝
```bash
# 检查Redis实例状态
aws ec2 describe-instances \
  --instance-ids <redis-instance-id> \
  --query 'Reservations[].Instances[].[State.Name,PrivateIpAddress]'

# 测试网络连通性 (从ECS任务)
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task <ecs-task-arn> \
  --container yherp \
  --interactive \
  --command "telnet redis-claude-relay.yuanhui-dev.local 6379"

# 检查安全组规则
aws ec2 describe-security-groups \
  --group-ids <redis-security-group-id> \
  --query 'SecurityGroups[].IpPermissions'
```

#### 3. 内存不足和性能问题
```bash
# 查看各实例内存使用
redis-cli -p 6379 INFO memory | grep -E "(used_memory_human|maxmemory_human)"
redis-cli -p 6380 INFO memory | grep -E "(used_memory_human|maxmemory_human)"
redis-cli -p 6381 INFO memory | grep -E "(used_memory_human|maxmemory_human)"

# 检查内存策略
redis-cli -p 6379 CONFIG GET maxmemory-policy
redis-cli -p 6380 CONFIG GET maxmemory-policy
redis-cli -p 6381 CONFIG GET maxmemory-policy

# 调整内存策略 (如需要)
redis-cli -p 6379 CONFIG SET maxmemory-policy allkeys-lru
redis-cli -p 6380 CONFIG SET maxmemory-policy allkeys-lru
redis-cli -p 6381 CONFIG SET maxmemory-policy allkeys-lru

# 查看慢查询日志
redis-cli -p 6379 SLOWLOG GET 10
redis-cli -p 6380 SLOWLOG GET 10
redis-cli -p 6381 SLOWLOG GET 10

# 监控实时命令执行
redis-cli -p 6379 MONITOR | head -100
```

#### 4. 持久化失败
```bash
# 检查磁盘空间
df -h /redis-data
df -h /

# 查看持久化状态
redis-cli -p 6379 INFO persistence
redis-cli -p 6380 INFO persistence

# 检查文件权限
ls -la /redis-data/
ls -la /redis-data/claude-relay/
ls -la /redis-data/session/
ls -la /redis-data/cache/

# 修复权限问题
sudo chown -R redis:redis /redis-data
sudo chmod -R 755 /redis-data

# 检查AOF文件完整性 (如启用)
redis-check-aof /redis-data/claude-relay/appendonly.aof
redis-check-aof /redis-data/session/appendonly.aof
```

#### 5. 主从复制问题 (生产环境)
```bash
# 检查主从复制状态
redis-cli -p 6379 INFO replication  # 主实例
redis-cli -p 6380 INFO replication  # 从实例

# 查看复制偏移量
redis-cli -p 6379 INFO replication | grep master_repl_offset
redis-cli -p 6380 INFO replication | grep slave_repl_offset

# 手动重新同步 (谨慎操作)
redis-cli -p 6380 SLAVEOF localhost 6379

# 检查网络连接
redis-cli -p 6380 INFO replication | grep master_link_status
```

### EBS卷和存储问题
```bash
# 检查EBS卷挂载状态
mount | grep /redis-data
lsblk

# 检查文件系统
sudo fsck /dev/nvme1n1

# 重新挂载EBS卷 (如需要)
sudo umount /redis-data
sudo mount /dev/nvme1n1 /redis-data

# 检查磁盘I/O性能
iostat -x 1 5
iotop -o

# 查看EBS卷性能指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/EBS \
  --metric-name VolumeReadOps \
  --dimensions Name=VolumeId,Value=<volume-id> \
  --start-time 2023-01-01T00:00:00Z \
  --end-time 2023-01-02T00:00:00Z \
  --period 3600 \
  --statistics Average
```

## 性能优化

### 内存优化 (按实例配置)

#### Claude Relay Redis优化 (关键数据)
```bash
# 连接到Claude Relay Redis
redis-cli -p 6379

# 启用内存压缩
CONFIG SET hash-max-ziplist-entries 512
CONFIG SET hash-max-ziplist-value 64

# 使用LRU策略但保留重要数据
CONFIG SET maxmemory-policy allkeys-lru

# 增加内存采样以提高淘汰精度
CONFIG SET maxmemory-samples 10

# 禁用键过期事件 (减少CPU开销)
CONFIG SET notify-keyspace-events ""
```

#### Session Redis优化 (会话数据)
```bash
# 连接到Session Redis
redis-cli -p 6380

# 针对会话数据优化
CONFIG SET maxmemory-policy volatile-ttl  # 优先淘汰即将过期的键

# 减少内存碎片
CONFIG SET activedefrag yes
CONFIG SET active-defrag-ignore-bytes 100mb

# 优化会话过期处理
CONFIG SET hz 100  # 增加后台任务频率
```

#### Cache Redis优化 (纯缓存)
```bash
# 连接到Cache Redis
redis-cli -p 6381

# 纯缓存策略 - 激进淘汰
CONFIG SET maxmemory-policy allkeys-lru

# 降低持久化开销 (如果禁用了持久化)
CONFIG SET save ""
CONFIG SET appendonly no

# 优化批量操作
CONFIG SET tcp-keepalive 60
```

### 持久化优化

#### AOF优化 (Claude Relay和Session)
```bash
# AOF重写优化 - 减少文件大小
redis-cli -p 6379 CONFIG SET auto-aof-rewrite-percentage 100
redis-cli -p 6379 CONFIG SET auto-aof-rewrite-min-size 64mb

redis-cli -p 6380 CONFIG SET auto-aof-rewrite-percentage 100
redis-cli -p 6380 CONFIG SET auto-aof-rewrite-min-size 32mb

# 后台AOF重写，降低阻塞
redis-cli -p 6379 CONFIG SET aof-rewrite-incremental-fsync yes
redis-cli -p 6380 CONFIG SET aof-rewrite-incremental-fsync yes
```

#### RDB优化 (所有实例)
```bash
# 根据业务重要性调整RDB频率
# Claude Relay - 频繁保存
redis-cli -p 6379 CONFIG SET save "300 1 600 100 900 1000"

# Session - 中等频率
redis-cli -p 6380 CONFIG SET save "600 1 900 100"

# Cache - 禁用RDB
redis-cli -p 6381 CONFIG SET save ""
```

### 网络和连接优化
```bash
# 所有Redis实例优化连接
for port in 6379 6380 6381; do
    redis-cli -p $port CONFIG SET tcp-keepalive 300
    redis-cli -p $port CONFIG SET timeout 300
    redis-cli -p $port CONFIG SET tcp-backlog 511
done

# 优化客户端连接缓冲区
redis-cli -p 6379 CONFIG SET client-output-buffer-limit "normal 0 0 0 slave 268435456 67108864 60 pubsub 33554432 8388608 60"
```

## 监控和告警

### CloudWatch集成

#### EC2实例监控
- **实例状态**: EC2实例运行状态和健康检查
- **系统资源**: CPU使用率、内存使用率、网络I/O
- **EBS卷性能**: IOPS、吞吐量、队列深度

#### 自定义监控脚本
```python
#!/usr/bin/env python3
# /home/<USER>/redis-monitoring.py
import redis
import boto3
import json
from datetime import datetime

# CloudWatch客户端
cloudwatch = boto3.client('cloudwatch')

# Redis实例配置
REDIS_INSTANCES = {
    'claude-relay': {'port': 6379, 'namespace': 'Redis/ClaudeRelay'},
    'session': {'port': 6380, 'namespace': 'Redis/Session'},
    'cache': {'port': 6381, 'namespace': 'Redis/Cache'}
}

def send_metric(namespace, metric_name, value, unit='Count', dimensions=None):
    """发送自定义指标到CloudWatch"""
    try:
        cloudwatch.put_metric_data(
            Namespace=namespace,
            MetricData=[
                {
                    'MetricName': metric_name,
                    'Value': value,
                    'Unit': unit,
                    'Timestamp': datetime.utcnow(),
                    'Dimensions': dimensions or []
                }
            ]
        )
    except Exception as e:
        print(f"Failed to send metric {metric_name}: {e}")

def monitor_redis_instance(name, config):
    """监控指定Redis实例"""
    try:
        r = redis.Redis(host='localhost', port=config['port'], db=0, socket_timeout=5)

        # 测试连接
        r.ping()

        # 获取基本信息
        info = r.info()

        # 发送关键指标到CloudWatch
        metrics = [
            ('ConnectedClients', info['connected_clients'], 'Count'),
            ('UsedMemory', info['used_memory'], 'Bytes'),
            ('KeyspaceHits', info['keyspace_hits'], 'Count'),
            ('KeyspaceMisses', info['keyspace_misses'], 'Count'),
            ('TotalCommandsProcessed', info['total_commands_processed'], 'Count'),
            ('RejectedConnections', info['rejected_connections'], 'Count'),
            ('UsedCpuSys', float(info['used_cpu_sys']), 'Percent'),
            ('UsedCpuUser', float(info['used_cpu_user']), 'Percent'),
        ]

        # 计算缓存命中率
        total_requests = info['keyspace_hits'] + info['keyspace_misses']
        if total_requests > 0:
            hit_rate = info['keyspace_hits'] / total_requests * 100
            metrics.append(('CacheHitRate', hit_rate, 'Percent'))

        # 获取数据库大小
        dbsize = r.dbsize()
        metrics.append(('DatabaseSize', dbsize, 'Count'))

        # 发送指标
        dimensions = [{'Name': 'InstanceName', 'Value': name}]
        for metric_name, value, unit in metrics:
            send_metric(config['namespace'], metric_name, value, unit, dimensions)

        print(f"✓ {name}: {info['used_memory_human']} memory, {dbsize} keys, {hit_rate:.2f}% hit rate" if total_requests > 0 else f"✓ {name}: {info['used_memory_human']} memory, {dbsize} keys")

        return True

    except Exception as e:
        print(f"✗ {name}: {e}")
        # 发送连接失败指标
        send_metric(config['namespace'], 'ConnectionFailure', 1, 'Count',
                   [{'Name': 'InstanceName', 'Value': name}])
        return False

def main():
    print(f"Redis monitoring started at {datetime.now()}")

    success_count = 0
    for name, config in REDIS_INSTANCES.items():
        if monitor_redis_instance(name, config):
            success_count += 1

    # 发送总体健康指标
    send_metric('Redis/Overall', 'HealthyInstances', success_count, 'Count')
    send_metric('Redis/Overall', 'TotalInstances', len(REDIS_INSTANCES), 'Count')

    print(f"Monitoring completed: {success_count}/{len(REDIS_INSTANCES)} instances healthy")

if __name__ == "__main__":
    main()
```

#### CloudWatch告警配置
```bash
# 创建Redis内存使用告警
aws cloudwatch put-metric-alarm \
  --alarm-name "Redis-ClaudeRelay-HighMemoryUsage" \
  --alarm-description "Redis Claude Relay instance memory usage is high" \
  --metric-name UsedMemory \
  --namespace Redis/ClaudeRelay \
  --statistic Average \
  --period 300 \
  --threshold ********** \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2 \
  --alarm-actions arn:aws:sns:region:account:redis-alerts

# 创建连接失败告警
aws cloudwatch put-metric-alarm \
  --alarm-name "Redis-ConnectionFailure" \
  --alarm-description "Redis connection failure detected" \
  --metric-name ConnectionFailure \
  --namespace Redis/Overall \
  --statistic Sum \
  --period 60 \
  --threshold 0 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 1 \
  --treat-missing-data notBreaching \
  --alarm-actions arn:aws:sns:region:account:redis-alerts

# 创建缓存命中率告警
aws cloudwatch put-metric-alarm \
  --alarm-name "Redis-Cache-LowHitRate" \
  --alarm-description "Redis cache hit rate is low" \
  --metric-name CacheHitRate \
  --namespace Redis/Cache \
  --statistic Average \
  --period 900 \
  --threshold 80 \
  --comparison-operator LessThanThreshold \
  --evaluation-periods 3 \
  --alarm-actions arn:aws:sns:region:account:redis-alerts
```

### 告警场景和阈值
- **内存使用率 > 80%**: Claude Relay和Session Redis
- **缓存命中率 < 80%**: Cache Redis 15分钟平均值
- **连接失败**: 任何Redis实例连接失败
- **主从延迟 > 60秒**: 生产环境主从复制延迟
- **磁盘使用率 > 85%**: EBS卷空间使用
- **实例状态检查失败**: EC2实例健康检查

## 安全配置

### 基本安全设置

#### 生产环境安全强化
```bash
# 连接到各Redis实例并设置安全配置
for port in 6379 6380 6381; do
    echo "Configuring security for Redis on port $port..."

    # 禁用危险命令
    redis-cli -p $port CONFIG SET rename-command FLUSHDB "FLUSHDB_DISABLED_$(openssl rand -hex 8)"
    redis-cli -p $port CONFIG SET rename-command FLUSHALL "FLUSHALL_DISABLED_$(openssl rand -hex 8)"
    redis-cli -p $port CONFIG SET rename-command DEBUG "DEBUG_DISABLED_$(openssl rand -hex 8)"
    redis-cli -p $port CONFIG SET rename-command CONFIG "CONFIG_$(openssl rand -hex 8)"

    # 限制客户端连接数
    redis-cli -p $port CONFIG SET maxclients 1000

    # 设置慢查询日志
    redis-cli -p $port CONFIG SET slowlog-log-slower-than 10000
    redis-cli -p $port CONFIG SET slowlog-max-len 128
done

# 设置生产环境密码认证 (如果启用)
if [ "$ENVIRONMENT" = "prod" ]; then
    # 从AWS Secrets Manager获取密码
    REDIS_PASSWORD=$(aws secretsmanager get-secret-value \
        --secret-id redis-auth-password \
        --query SecretString --output text | jq -r .password)

    redis-cli -p 6379 CONFIG SET requirepass "$REDIS_PASSWORD"
    redis-cli -p 6381 CONFIG SET requirepass "$REDIS_PASSWORD"
fi
```

### 网络安全

#### 安全组配置
- **入站规则**: 仅允许ECS安全组访问Redis端口 (6379-6383)
- **出站规则**: 允许所有出站流量 (用于复制和监控)
- **SSH访问**: 仅允许VPC内部网络 (10.0.0.0/8) 访问22端口

#### 网络隔离
- **私有子网部署**: Redis实例部署在私有子网，无公网访问
- **VPC端点**: 通过VPC端点访问AWS服务，减少互联网流量
- **Service Connect**: 应用通过ECS Service Connect内部域名访问
- **防火墙规则**: 使用iptables额外限制访问 (如需要)

### 数据保护

#### 静态数据加密
```bash
# 检查EBS卷加密状态
aws ec2 describe-volumes \
  --volume-ids $(aws ec2 describe-instances \
    --instance-ids <redis-instance-id> \
    --query 'Reservations[].Instances[].BlockDeviceMappings[].Ebs.VolumeId' \
    --output text) \
  --query 'Volumes[].[VolumeId,Encrypted,KmsKeyId]' \
  --output table

# 生产环境EBS卷应该启用加密
# 数据目录: /redis-data (加密EBS卷)
# 配置文件: /etc/redis/*.conf (包含敏感配置)
```

#### 传输加密 (生产环境)
如果启用TLS (生产环境可选):
```bash
# 生成自签名证书 (或使用CA证书)
openssl req -x509 -nodes -newkey rsa:4096 \
  -keyout /etc/redis/tls/redis.key \
  -out /etc/redis/tls/redis.crt \
  -days 365 \
  -subj "/CN=redis.yuanhui-prod.local"

# 在Redis配置中启用TLS
echo "tls-port 6380" >> /etc/redis/claude-relay.conf
echo "tls-cert-file /etc/redis/tls/redis.crt" >> /etc/redis/claude-relay.conf
echo "tls-key-file /etc/redis/tls/redis.key" >> /etc/redis/claude-relay.conf

# 重启服务
sudo systemctl restart redis-claude-relay
```

## 相关文档

- [EC2 Redis Stack配置](../../lib/stacks/redis-stack.ts) - Redis Stack CDK实现
- [Redis配置接口](../../lib/config/stacks/redis.ts) - 配置类型定义
- [开发环境配置](../../lib/config/environments/dev/redis.ts) - 开发环境Redis配置
- [生产环境配置](../../lib/config/environments/prod/redis.ts) - 生产环境Redis配置
- [网络架构文档](../../architecture/network.md) - VPC和网络配置
- [监控配置](../../operations/README.md) - CloudWatch监控设置
- [部署指南](../../deployment/README.md) - Redis Stack部署流程

## 版本历史

- **v1.0**: 基于ECS容器的Redis部署
- **v2.0**: 迁移到EC2实例部署，支持多Redis实例按业务分离
- **v2.1**: 增加主从复制支持 (生产环境)
- **v2.2**: 完善监控和告警集成
- **当前**: 完整的企业级Redis解决方案，支持Claude Relay、Session和Cache业务分离